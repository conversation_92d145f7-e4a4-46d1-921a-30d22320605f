# RBAC Backend Requirements

## Overview

This document outlines the backend requirements for the Role-Based Access Control (RBAC) system. The frontend is already implemented and expects the backend to provide specific API endpoints and functionality.

## Database Schema

### Users Table
```sql
CREATE TABLE users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  name VA<PERSON>HA<PERSON>(100) NOT NULL,
  phone VARCHAR(20),
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Roles Table
```sql
CREATE TABLE roles (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(50) UNIQUE NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### User_Roles Table
```sql
CREATE TABLE user_roles (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  role_id INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
  UNIQUE KEY (user_id, role_id)
);
```

### Modules Table
```sql
CREATE TABLE modules (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(50) UNIQUE NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_by INT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);
```

### Permissions Table
```sql
CREATE TABLE permissions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(50) UNIQUE NOT NULL,
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### Role_Module_Permissions Table
```sql
CREATE TABLE role_module_permissions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  role_id INT NOT NULL,
  module_id INT NOT NULL,
  permission_id INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
  FOREIGN KEY (module_id) REFERENCES modules(id) ON DELETE CASCADE,
  FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
  UNIQUE KEY (role_id, module_id, permission_id)
);
```

## Initial Data

### Default Permissions
The system requires four standard permissions:

```sql
INSERT INTO permissions (name, description) VALUES
('create', 'Permission to create new records'),
('read', 'Permission to view records'),
('update', 'Permission to modify existing records'),
('delete', 'Permission to remove records');
```

### Default Admin Role
```sql
INSERT INTO roles (name, description) VALUES
('admin', 'Administrator with full system access');
```

### Default Modules
```sql
INSERT INTO modules (name, description) VALUES
('user', 'User management module'),
('role', 'Role management module'),
('module', 'Module management module'),
('permission', 'Permission management module'),
('dashboard', 'Dashboard module');
```

### Default Admin Permissions
Grant all permissions to the admin role for all modules:

```sql
-- Assuming admin role has id=1, and the modules and permissions have been inserted
-- This would need to be adjusted based on actual IDs in your database

-- For each module and permission combination
INSERT INTO role_module_permissions (role_id, module_id, permission_id)
SELECT 1, m.id, p.id
FROM modules m
CROSS JOIN permissions p;
```

## API Endpoints

### Authentication Endpoints

#### Login
- **URL**: `/api/auth/login`
- **Method**: `POST`
- **Request Body**:
  ```json
  {
    "identifier": "string", // Username or email
    "password": "string"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "Login successful",
    "data": {
      "accessToken": "jwt_token_here",
      "refreshToken": "refresh_token_here",
      "user": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "name": "Admin User",
        "roles": ["admin"]
      }
    }
  }
  ```

#### Refresh Token
- **URL**: `/api/auth/refresh-token`
- **Method**: `POST`
- **Request Body**:
  ```json
  {
    "refreshToken": "string"
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "Token refreshed",
    "data": {
      "accessToken": "new_jwt_token_here",
      "refreshToken": "new_refresh_token_here"
    }
  }
  ```

#### Check Permission
- **URL**: `/api/auth/check-permission`
- **Method**: `POST`
- **Headers**: `Authorization: Bearer <token>`
- **Request Body**:
  ```json
  {
    "module": "string",
    "permission": "string" // "create", "read", "update", or "delete"
  }
  ```
- **Response**:
  ```json
  {
    "hasPermission": true|false
  }
  ```

### User Management Endpoints

#### Get All Users
- **URL**: `/api/users`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer <token>`
- **Query Parameters**:
  - `includeInactive` (optional): Boolean to include inactive users
- **Response**:
  ```json
  {
    "success": true,
    "data": [
      {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "name": "Admin User",
        "phone": "+1234567890",
        "is_active": true,
        "roles": ["admin"],
        "created_at": "2023-01-01T00:00:00Z",
        "updated_at": "2023-01-01T00:00:00Z"
      }
    ]
  }
  ```

#### Get User by ID
- **URL**: `/api/users/:id`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer <token>`
- **Response**:
  ```json
  {
    "success": true,
    "data": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "name": "Admin User",
      "phone": "+1234567890",
      "is_active": true,
      "roles": ["admin"],
      "created_at": "2023-01-01T00:00:00Z",
      "updated_at": "2023-01-01T00:00:00Z"
    }
  }
  ```

#### Create User
- **URL**: `/api/users`
- **Method**: `POST`
- **Headers**: `Authorization: Bearer <token>`
- **Request Body**:
  ```json
  {
    "username": "string",
    "email": "string",
    "password": "string",
    "name": "string",
    "phone": "string" (optional),
    "is_active": boolean (optional),
    "roles": ["role1", "role2"] (optional)
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "User created successfully",
    "data": {
      "id": 2,
      "username": "newuser",
      "email": "<EMAIL>",
      "name": "New User",
      "phone": null,
      "is_active": true,
      "roles": [],
      "created_at": "2023-01-02T00:00:00Z",
      "updated_at": "2023-01-02T00:00:00Z"
    }
  }
  ```

#### Update User
- **URL**: `/api/users/:id`
- **Method**: `PUT`
- **Headers**: `Authorization: Bearer <token>`
- **Request Body**:
  ```json
  {
    "username": "string" (optional),
    "email": "string" (optional),
    "password": "string" (optional),
    "name": "string" (optional),
    "phone": "string" (optional),
    "is_active": boolean (optional),
    "roles": ["role1", "role2"] (optional)
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "User updated successfully",
    "data": {
      "id": 2,
      "username": "updateduser",
      "email": "<EMAIL>",
      "name": "Updated User",
      "phone": "+9876543210",
      "is_active": true,
      "roles": ["role1"],
      "created_at": "2023-01-02T00:00:00Z",
      "updated_at": "2023-01-03T00:00:00Z"
    }
  }
  ```

#### Delete User
- **URL**: `/api/users/:id`
- **Method**: `DELETE`
- **Headers**: `Authorization: Bearer <token>`
- **Response**:
  ```json
  {
    "success": true,
    "message": "User deleted successfully"
  }
  ```

#### Toggle User Status
- **URL**: `/api/users/:id/status`
- **Method**: `PATCH`
- **Headers**: `Authorization: Bearer <token>`
- **Request Body**:
  ```json
  {
    "is_active": boolean
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "User status updated successfully",
    "data": {
      "id": 2,
      "is_active": false
    }
  }
  ```
