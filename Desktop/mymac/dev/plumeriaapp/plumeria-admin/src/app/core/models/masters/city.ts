export interface City {
  id: number;
  name: string;
  code: string;
  state_id: number;
  state_name?: string;
  country_name?: string;
  is_active: boolean;
  created_by?: number;
  updated_by?: number;
  created_at?: string;
  updated_at?: string;
  created_by_username?: string;
  updated_by_username?: string;
}

export interface CityResponse {
  success: boolean;
  data: City | City[];
  message?: string;
}
