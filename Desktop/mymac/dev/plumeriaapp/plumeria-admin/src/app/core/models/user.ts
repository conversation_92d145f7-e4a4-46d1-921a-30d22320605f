export interface User {
  id: number;
  username: string;
  email: string;
  name?: string;
  phone?: string;
  is_active?: boolean;
  roles: string[];
}

export interface UserRole {
  userId: number;
  roleId: number;
  roleName?: string;
}

export interface Role {
  id: number;
  name: string;
  description?: string;
  is_active: boolean;
  created_by?: number;
  created_at?: string;
  updated_at?: string;
  creator_name?: string;
}
