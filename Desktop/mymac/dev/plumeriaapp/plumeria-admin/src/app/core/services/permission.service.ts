import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError, of } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { Permission, RoleModulePermission } from '../models/permission';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class PermissionService {
  private apiUrl = `${environment.apiUrl}/role-module-permissions`;

  constructor(private http: HttpClient) {}

  // Get all permissions
  getPermissions(): Observable<{ success: boolean; data: Permission[] }> {
    console.log('Fetching all permissions from API');
    return this.http
      .get<{ success: boolean; data: Permission[] }>(
        `${environment.apiUrl}/permissions`
      )
      .pipe(
        tap((response) => console.log('Permissions API response:', response)),
        catchError((error) => {
          console.error('Error fetching permissions:', error);
          return throwError(() => error);
        })
      );
  }

  // Get all role-module-permissions
  getAllRoleModulePermissions(): Observable<{
    success: boolean;
    data: RoleModulePermission[];
  }> {
    console.log('Fetching all role-module-permissions from API');
    return this.http
      .get<{ success: boolean; data: RoleModulePermission[] }>(this.apiUrl)
      .pipe(
        tap((response) =>
          console.log('Role-Module-Permissions API response:', response)
        ),
        catchError((error) => {
          console.error('Error fetching role-module-permissions:', error);
          return throwError(() => error);
        })
      );
  }

  // Get permissions for a role
  getRolePermissions(
    roleId: number
  ): Observable<{ success: boolean; data: RoleModulePermission[] }> {
    console.log(`Fetching permissions for role ${roleId}`);
    return this.http
      .get<{ success: boolean; data: RoleModulePermission[] }>(
        `${this.apiUrl}/roles/${roleId}`
      )
      .pipe(
        tap((response) =>
          console.log(`Permissions for role ${roleId}:`, response)
        ),
        catchError((error) => {
          console.error(
            `Error fetching permissions for role ${roleId}:`,
            error
          );
          return throwError(() => error);
        })
      );
  }

  // Get permissions for a role-module combination
  getRoleModulePermissions(
    roleId: number,
    moduleId: number
  ): Observable<{ success: boolean; data: any }> {
    console.log(
      `Fetching permissions for role ${roleId} and module ${moduleId}`
    );
    return this.http
      .get<{ success: boolean; data: any }>(
        `${this.apiUrl}/roles/${roleId}/modules/${moduleId}`
      )
      .pipe(
        tap((response) => {
          console.log(
            `Permissions for role ${roleId}, module ${moduleId}:`,
            response
          );

          // Log detailed structure to help debug
          if (response && response.data) {
            if (Array.isArray(response.data)) {
              console.log(`Received array with ${response.data.length} items`);
              if (response.data.length > 0) {
                console.log(
                  'First item structure:',
                  JSON.stringify(response.data[0])
                );
              }
            } else {
              console.log(
                'Received object structure:',
                JSON.stringify(response.data)
              );
            }
          }
        }),
        catchError((error) => {
          console.error(
            `Error fetching permissions for role ${roleId}, module ${moduleId}:`,
            error
          );
          return throwError(() => error);
        })
      );
  }

  // Assign permission to role for a module
  assignPermission(
    roleId: number,
    moduleId: number,
    permissionId: number
  ): Observable<{ success: boolean; message: string }> {
    console.log(
      `Assigning permission ${permissionId} to role ${roleId} for module ${moduleId}`
    );
    return this.http
      .post<{ success: boolean; message: string }>(
        `${this.apiUrl}/roles/${roleId}/modules/${moduleId}/permissions/${permissionId}`,
        {}
      )
      .pipe(
        tap((response) =>
          console.log(
            `Assigned permission ${permissionId} to role ${roleId} for module ${moduleId}:`,
            response
          )
        ),
        catchError((error) => {
          console.error(
            `Error assigning permission ${permissionId} to role ${roleId} for module ${moduleId}:`,
            error
          );

          // Check specific error conditions
          if (error.status === 409) {
            console.log('Permission already exists - treating as success');
            return of({ success: true, message: 'Permission already exists' });
          }

          return throwError(() => error);
        })
      );
  }

  // Remove permission from role for a module
  removePermission(
    roleId: number,
    moduleId: number,
    permissionId: number
  ): Observable<{ success: boolean; message: string }> {
    console.log(
      `Removing permission ${permissionId} from role ${roleId} for module ${moduleId}`
    );
    return this.http
      .delete<{ success: boolean; message: string }>(
        `${this.apiUrl}/roles/${roleId}/modules/${moduleId}/permissions/${permissionId}`
      )
      .pipe(
        tap((response) =>
          console.log(
            `Removed permission ${permissionId} from role ${roleId} for module ${moduleId}:`,
            response
          )
        ),
        catchError((error) => {
          console.error(
            `Error removing permission ${permissionId} from role ${roleId} for module ${moduleId}:`,
            error
          );

          // If it's a 404 Not Found, it might mean the permission doesn't exist
          // We can treat this as a success in some cases
          if (error.status === 404) {
            console.log(
              'Permission not found (404), treating as already removed'
            );
            return of({ success: true, message: 'Permission already removed' });
          }

          return throwError(() => error);
        })
      );
  }

  // Assign all module permissions to role
  assignAllModulePermissions(
    roleId: number,
    moduleId: number
  ): Observable<{ success: boolean; message: string; data?: any }> {
    console.log(
      `Assigning all permissions to role ${roleId} for module ${moduleId}`
    );
    return this.http
      .post<{ success: boolean; message: string; data?: any }>(
        `${this.apiUrl}/roles/${roleId}/modules/${moduleId}/permissions`,
        {}
      )
      .pipe(
        tap((response) => {
          console.log(
            `Assigned all permissions to role ${roleId} for module ${moduleId}:`,
            response
          );

          // Log additional information if available
          if (response && response.data) {
            console.log('Response data:', response.data);
          }
        }),
        catchError((error) => {
          console.error(
            `Error assigning all permissions to role ${roleId} for module ${moduleId}:`,
            error
          );

          // Check for specific errors and provide more context
          if (error.status === 403) {
            console.error(
              'Permission denied - you might not have permission to do this'
            );
          } else if (error.status === 500) {
            console.error('Server error - check server logs');
          }

          return throwError(() => error);
        })
      );
  }

  // Remove all module permissions from role
  removeAllModulePermissions(
    roleId: number,
    moduleId: number
  ): Observable<{ success: boolean; message: string }> {
    console.log(
      `Removing all permissions from role ${roleId} for module ${moduleId}`
    );
    return this.http
      .delete<{ success: boolean; message: string }>(
        `${this.apiUrl}/roles/${roleId}/modules/${moduleId}/permissions`
      )
      .pipe(
        tap((response) =>
          console.log(
            `Removed all permissions from role ${roleId} for module ${moduleId}:`,
            response
          )
        ),
        catchError((error) => {
          console.error(
            `Error removing all permissions from role ${roleId} for module ${moduleId}:`,
            error
          );

          // Enhanced error reporting
          if (error.status === 404) {
            console.log('No permissions found to remove');
          }

          return throwError(() => error);
        })
      );
  }
}
