// src/app/core/services/user.service.ts

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { User } from '../models/user';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  private apiUrl = `${environment.apiUrl}/users`;

  constructor(private http: HttpClient) {}

  // Get all users
  getUsers(): Observable<{ success: boolean; data: User[] }> {
    return this.http.get<{ success: boolean; data: User[] }>(this.apiUrl);
  }

  // Get user by ID
  getUserById(id: number): Observable<{ success: boolean; data: User }> {
    return this.http.get<{ success: boolean; data: User }>(
      `${this.apiUrl}/${id}`
    );
  }

  // Create new user
  createUser(
    user: Partial<User>
  ): Observable<{ success: boolean; message: string; data: User }> {
    return this.http.post<{ success: boolean; message: string; data: User }>(
      this.apiUrl,
      user
    );
  }

  // Update user
  updateUser(
    id: number,
    user: Partial<User>
  ): Observable<{ success: boolean; message: string; data: User }> {
    return this.http.put<{ success: boolean; message: string; data: User }>(
      `${this.apiUrl}/${id}`,
      user
    );
  }

  // Delete user
  deleteUser(id: number): Observable<{ success: boolean; message: string }> {
    return this.http.delete<{ success: boolean; message: string }>(
      `${this.apiUrl}/${id}`
    );
  }

  // Toggle user status
  toggleUserStatus(
    id: number,
    isActive: boolean
  ): Observable<{ success: boolean; message: string; data: User }> {
    return this.http.patch<{ success: boolean; message: string; data: User }>(
      `${this.apiUrl}/${id}/status`,
      { is_active: isActive }
    );
  }

  // Change user password
  changePassword(
    id: number,
    passwordData: { currentPassword: string; newPassword: string }
  ): Observable<{ success: boolean; message: string }> {
    return this.http.post<{ success: boolean; message: string }>(
      `${this.apiUrl}/${id}/change-password`,
      passwordData
    );
  }
}
