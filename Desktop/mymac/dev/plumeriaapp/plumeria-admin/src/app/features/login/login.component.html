<div class="login-container">
    <div class="login-card-wrapper">
      <mat-card class="login-card">
        <div class="login-header">
          <h1>Plumeria Contracting</h1>
        </div>
        
        <mat-card-content>
          <form [formGroup]="loginForm" (ngSubmit)="onSubmit()">
            <div class="form-field">
              <mat-form-field appearance="outline">
                <mat-label>Username or Email</mat-label>
                <input matInput formControlName="identifier" type="text" required>
                <mat-icon matPrefix>person</mat-icon>
                <mat-error *ngIf="loginForm.get('identifier')?.hasError('required')">
                  Username or email is required
                </mat-error>
              </mat-form-field>
            </div>
            
            <div class="form-field">
              <mat-form-field appearance="outline">
                <mat-label>Password</mat-label>
                <input matInput formControlName="password" [type]="hidePassword ? 'password' : 'text'" required>
                <mat-icon matPrefix>lock</mat-icon>
                <button mat-icon-button matSuffix type="button" (click)="hidePassword = !hidePassword">
                  <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
                </button>
                <mat-error *ngIf="loginForm.get('password')?.hasError('required')">
                  Password is required
                </mat-error>
              </mat-form-field>
            </div>
            
            <div class="error-message" *ngIf="error">
              <mat-icon>error</mat-icon> {{ error }}
            </div>
            
            <div class="form-actions">
              <button mat-raised-button color="primary" type="submit" [disabled]="loginForm.invalid || loading" class="login-button">
                <mat-spinner diameter="20" *ngIf="loading"></mat-spinner>
                <span *ngIf="!loading">Login</span>
              </button>
            </div>
            
            <div class="additional-actions">
              <a routerLink="/forgot-password">Forgot password?</a>
              <a routerLink="/register">Create an account</a>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  </div>