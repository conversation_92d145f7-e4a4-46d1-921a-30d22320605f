.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    // background-color: #f5f7fa;
    // background-image: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  }
  
  .login-card-wrapper {
    width: 100%;
    max-width: 420px;
    padding: 20px;
  }
  
  .login-card {
    border-radius: 8px;
    // box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }
  
  .login-header {
    text-align: center;
    padding: 24px 16px;
    background: #f5f5f5;
    border-bottom: 1px solid #eee;
    
    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
      color: #3f51b5;
    }
    
    p {
      margin: 8px 0 0;
      color: #666;
      font-size: 14px;
    }
  }
  
  mat-card-content {
    padding: 24px 16px !important;
  }
  
  .form-field {
    margin-bottom: 16px;
    
    mat-form-field {
      width: 100%;
    }
  }
  
  .error-message {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: #ffebee;
    border-radius: 4px;
    color: #d32f2f;
    margin-bottom: 16px;
    font-size: 14px;
    
    mat-icon {
      margin-right: 8px;
      font-size: 18px;
      height: 18px;
      width: 18px;
    }
  }
  
  .form-actions {
    margin-top: 24px;
    
    .login-button {
      width: 100%;
      padding: 8px;
      font-size: 16px;
      
      mat-spinner {
        display: inline-block;
        margin-right: 8px;
      }
    }
  }
  
  .additional-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 24px;
    
    a {
      color: #3f51b5;
      text-decoration: none;
      font-size: 14px;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }