import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';

import { BloodGroupService, BloodGroup } from '../../../../core/services/masters/blood-group.service';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-blood-group-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDialogModule,
    MatTooltipModule,
    MatDividerModule,
    MatChipsModule
  ],
  templateUrl: './blood-group-detail.component.html',
  styleUrls: ['./blood-group-detail.component.scss']
})
export class BloodGroupDetailComponent implements OnInit {
  bloodGroup: BloodGroup | null = null;
  isLoading = true;
  errorMessage = '';
  bloodGroupId: number | null = null;

  constructor(
    private bloodGroupService: BloodGroupService,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      const idParam = params.get('id');
      if (idParam) {
        this.bloodGroupId = +idParam;
        this.loadBloodGroupData(this.bloodGroupId);
      } else {
        this.errorMessage = 'Blood Group ID is missing';
        this.isLoading = false;
      }
    });
  }

  loadBloodGroupData(id: number): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.bloodGroupService.getBloodGroupById(id).subscribe({
      next: (response) => {
        if (response.success) {
          this.bloodGroup = response.data as BloodGroup;
        } else {
          this.errorMessage = response.message || 'Failed to load blood group details';
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Error loading blood group details: ' + this.getErrorMessage(error);
        this.isLoading = false;
        console.error('Error loading blood group details:', error);
      }
    });
  }

  toggleStatus(): void {
    if (!this.bloodGroup) return;

    const newStatus = !this.bloodGroup.is_active;
    const bloodGroupId = this.bloodGroup.id;
    
    this.bloodGroupService.toggleBloodGroupStatus(bloodGroupId, newStatus).subscribe({
      next: (response) => {
        if (response.success) {
          if (this.bloodGroup) {
            this.bloodGroup.is_active = newStatus;
          }
          this.showSnackBar(`Blood Group ${newStatus ? 'activated' : 'deactivated'} successfully`);
        } else {
          this.showSnackBar(`Failed to update status: ${response.message}`, true);
        }
      },
      error: (error) => {
        this.showSnackBar(`Error updating status: ${this.getErrorMessage(error)}`, true);
        console.error('Error toggling blood group status:', error);
      }
    });
  }

  deleteBloodGroup(): void {
    if (!this.bloodGroup) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the blood group "${this.bloodGroup.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && this.bloodGroup) {
        this.bloodGroupService.deleteBloodGroup(this.bloodGroup.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.showSnackBar(`Blood Group deleted successfully`);
              this.router.navigate(['../'], { relativeTo: this.route });
            } else {
              this.showSnackBar(`Failed to delete blood group: ${response.message}`, true);
            }
          },
          error: (error) => {
            this.showSnackBar(`Error deleting blood group: ${this.getErrorMessage(error)}`, true);
            console.error('Error deleting blood group:', error);
          }
        });
      }
    });
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }

  getErrorMessage(error: any): string {
    return error.error?.message || error.message || 'Unknown error occurred';
  }

  formatDate(dateString: string | undefined): string {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  }
}
