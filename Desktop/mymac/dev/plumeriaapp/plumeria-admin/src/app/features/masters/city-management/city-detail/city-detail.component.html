<div class="city-detail-container">
  <div class="detail-header">
    <h1>City Details</h1>
    <button mat-button color="primary" (click)="goBack()">
      <mat-icon>arrow_back</mat-icon> Back to Cities
    </button>
  </div>

  <mat-card>
    <mat-card-content>
      <!-- Loading spinner -->
      <div class="loading-spinner" *ngIf="isLoading">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading city details...</p>
      </div>

      <!-- Error message -->
      <div class="error-message" *ngIf="errorMessage">
        <mat-icon>error</mat-icon> {{ errorMessage }}
      </div>

      <!-- City details -->
      <div class="city-info" *ngIf="!isLoading && city">
        <div class="city-header">
          <h2>{{ city.name }}</h2>
          <mat-chip-set>
            <mat-chip [color]="city.is_active ? 'primary' : 'warn'" selected>
              {{ city.is_active ? 'Active' : 'Inactive' }}
            </mat-chip>
          </mat-chip-set>
        </div>

        <mat-divider></mat-divider>

        <div class="city-details">
          <div class="detail-item">
            <span class="label">ID:</span>
            <span class="value">{{ city.id }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Name:</span>
            <span class="value">{{ city.name }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Code:</span>
            <span class="value">{{ city.code }}</span>
          </div>
          <div class="detail-item">
            <span class="label">State:</span>
            <span class="value">{{ city.state_name }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Country:</span>
            <span class="value">{{ city.country_name }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Status:</span>
            <span class="value">{{ city.is_active ? 'Active' : 'Inactive' }}</span>
          </div>
          <div class="detail-item" *ngIf="city.created_by_username">
            <span class="label">Created By:</span>
            <span class="value">{{ city.created_by_username }}</span>
          </div>
          <div class="detail-item" *ngIf="city.created_at">
            <span class="label">Created At:</span>
            <span class="value">{{ city.created_at | date:'medium' }}</span>
          </div>
          <div class="detail-item" *ngIf="city.updated_by_username">
            <span class="label">Updated By:</span>
            <span class="value">{{ city.updated_by_username }}</span>
          </div>
          <div class="detail-item" *ngIf="city.updated_at">
            <span class="label">Updated At:</span>
            <span class="value">{{ city.updated_at | date:'medium' }}</span>
          </div>
        </div>

        <div class="detail-actions" *ngIf="!isLoading && city">
          <button mat-raised-button color="warn" (click)="deleteCity()">
            <mat-icon>delete</mat-icon> Delete
          </button>
          <button mat-raised-button color="accent" (click)="toggleStatus()">
            <mat-icon>{{ city.is_active ? 'toggle_off' : 'toggle_on' }}</mat-icon>
            {{ city.is_active ? 'Deactivate' : 'Activate' }}
          </button>
          <button mat-raised-button color="primary" [routerLink]="['../../edit', city.id]">
            <mat-icon>edit</mat-icon> Edit
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
