import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';

import { CityService } from '../../../../core/services/masters/city.service';
import { City } from '../../../../core/models/masters/city';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-city-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatDividerModule,
    MatSnackBarModule,
    MatDialogModule
  ],
  templateUrl: './city-detail.component.html',
  styleUrls: ['./city-detail.component.scss']
})
export class CityDetailComponent implements OnInit {
  city: City | null = null;
  isLoading = true;
  errorMessage = '';

  constructor(
    private cityService: CityService,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.loadCityData(+id);
      } else {
        this.errorMessage = 'No city ID provided';
        this.isLoading = false;
      }
    });
  }

  loadCityData(id: number): void {
    this.isLoading = true;
    this.errorMessage = '';
    
    this.cityService.getCityById(id).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.city = response.data as City;
        } else {
          this.errorMessage = response.message || 'Failed to load city data';
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Error loading city data: ' + this.getErrorMessage(error);
        this.isLoading = false;
        console.error('Error loading city data:', error);
      }
    });
  }

  toggleStatus(): void {
    if (!this.city) return;
    
    const newStatus = !this.city.is_active;
    
    this.cityService.toggleCityStatus(this.city.id, newStatus).subscribe({
      next: (response) => {
        if (response.success) {
          if (this.city) {
            this.city.is_active = newStatus;
          }
          this.showSnackBar(`City ${newStatus ? 'activated' : 'deactivated'} successfully`);
        } else {
          this.showSnackBar(`Failed to update status: ${response.message}`, true);
        }
      },
      error: (error) => {
        this.showSnackBar(`Error updating status: ${this.getErrorMessage(error)}`, true);
        console.error('Error toggling city status:', error);
      }
    });
  }

  deleteCity(): void {
    if (!this.city) return;
    
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the city "${this.city.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && this.city) {
        this.cityService.deleteCity(this.city.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.showSnackBar(`City ${this.city?.name} deleted successfully`);
              this.router.navigate(['../../'], { relativeTo: this.route });
            } else {
              this.showSnackBar(`Failed to delete city: ${response.message}`, true);
            }
          },
          error: (error) => {
            this.showSnackBar(`Error deleting city: ${this.getErrorMessage(error)}`, true);
            console.error('Error deleting city:', error);
          }
        });
      }
    });
  }

  goBack(): void {
    this.router.navigate(['../../'], { relativeTo: this.route });
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }

  getErrorMessage(error: any): string {
    return error.error?.message || error.message || 'Unknown error occurred';
  }
}
