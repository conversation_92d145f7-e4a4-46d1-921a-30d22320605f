<div class="city-form-container">
  <mat-card>
    <mat-card-header>
      <mat-card-title>{{ isEditMode ? 'Edit City' : 'Add New City' }}</mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <div *ngIf="isLoading" class="loading-spinner">
        <mat-spinner diameter="40"></mat-spinner>
      </div>

      <div *ngIf="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <form [formGroup]="cityForm" (ngSubmit)="onSubmit()" *ngIf="!isLoading">
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>City Name</mat-label>
            <input matInput formControlName="name" placeholder="Enter city name">
            <mat-error *ngIf="nameControl?.hasError('required')">
              City name is required
            </mat-error>
            <mat-error *ngIf="nameControl?.hasError('minlength')">
              City name must be at least 2 characters
            </mat-error>
            <mat-error *ngIf="nameControl?.hasError('maxlength')">
              City name cannot exceed 100 characters
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>City Code</mat-label>
            <input matInput formControlName="code" placeholder="Enter city code (e.g., NYC, LAX)">
            <mat-error *ngIf="codeControl?.hasError('required')">
              City code is required
            </mat-error>
            <mat-error *ngIf="codeControl?.hasError('minlength')">
              City code must be at least 1 character
            </mat-error>
            <mat-error *ngIf="codeControl?.hasError('maxlength')">
              City code cannot exceed 10 characters
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>State</mat-label>
            <mat-select formControlName="state_id">
              <mat-option *ngFor="let state of states" [value]="state.id">
                {{ state.name }} ({{ state.country_name }})
              </mat-option>
            </mat-select>
            <mat-error *ngIf="stateIdControl?.hasError('required')">
              State is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-slide-toggle formControlName="is_active" color="primary">
            {{ cityForm.get('is_active')?.value ? 'Active' : 'Inactive' }}
          </mat-slide-toggle>
        </div>
      </form>
    </mat-card-content>

    <mat-card-actions align="end">
      <button mat-button type="button" routerLink="..">Cancel</button>
      <button mat-raised-button color="primary" [disabled]="isLoading || cityForm.invalid" (click)="onSubmit()">
        {{ isEditMode ? 'Update' : 'Create' }}
      </button>
    </mat-card-actions>
  </mat-card>
</div>
