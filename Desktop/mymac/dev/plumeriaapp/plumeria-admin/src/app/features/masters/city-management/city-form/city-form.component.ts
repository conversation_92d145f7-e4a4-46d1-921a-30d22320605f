import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatSelectModule } from '@angular/material/select';

import { CityService } from '../../../../core/services/masters/city.service';
import { StateService } from '../../../../core/services/masters/state.service';
import { CountryService } from '../../../../core/services/masters/country.service';
import { City } from '../../../../core/models/masters/city';
import { State } from '../../../../core/models/masters/state';
import { Country } from '../../../../core/models/masters/country';

@Component({
  selector: 'app-city-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatSelectModule,
  ],
  templateUrl: './city-form.component.html',
  styleUrls: ['./city-form.component.scss'],
})
export class CityFormComponent implements OnInit {
  cityForm!: FormGroup;
  isEditMode = false;
  cityId: number | null = null;
  isLoading = false;
  errorMessage = '';

  countries: Country[] = [];
  states: State[] = [];
  loadingCountries = false;
  loadingStates = false;

  constructor(
    private fb: FormBuilder,
    private cityService: CityService,
    private stateService: StateService,
    private countryService: CountryService,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadStates();

    // Check if we're in edit mode
    this.route.paramMap.subscribe((params) => {
      const id = params.get('id');
      if (id) {
        this.isEditMode = true;
        this.cityId = +id;
        this.loadCityData(+id);
      }
    });
  }

  initForm(): void {
    this.cityForm = this.fb.group({
      name: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(100),
        ],
      ],
      code: [
        '',
        [
          Validators.required,
          Validators.minLength(1),
          Validators.maxLength(10),
        ],
      ],
      state_id: ['', [Validators.required]],
      is_active: [true],
    });
  }

  loadCountries(): void {
    this.loadingCountries = true;

    this.countryService.getCountries(true).subscribe({
      next: (response) => {
        if (response.success) {
          this.countries = Array.isArray(response.data) ? response.data : [];
        } else {
          this.errorMessage = response.message || 'Failed to load countries';
        }
        this.loadingCountries = false;
      },
      error: (error) => {
        this.errorMessage =
          'Error loading countries: ' + this.getErrorMessage(error);
        this.loadingCountries = false;
        console.error('Error loading countries:', error);
      },
    });
  }

  loadStates(): void {
    this.loadingStates = true;
    this.states = [];

    this.stateService.getStates(true).subscribe({
      next: (response) => {
        if (response.success) {
          this.states = Array.isArray(response.data) ? response.data : [];
        } else {
          this.errorMessage = response.message || 'Failed to load states';
        }
        this.loadingStates = false;
      },
      error: (error) => {
        this.errorMessage =
          'Error loading states: ' + this.getErrorMessage(error);
        this.loadingStates = false;
        console.error('Error loading states:', error);
      },
    });
  }

  loadCityData(id: number): void {
    this.isLoading = true;

    // Load all states first
    this.loadStates();

    this.cityService.getCityById(id).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          const city = response.data as City;

          // Set the form values
          this.cityForm.patchValue({
            name: city.name,
            code: city.code,
            state_id: city.state_id,
            is_active: city.is_active,
          });

          this.isLoading = false;
        } else {
          this.errorMessage = response.message || 'Failed to load city data';
          this.isLoading = false;
        }
      },
      error: (error) => {
        this.errorMessage =
          'Error loading city data: ' + this.getErrorMessage(error);
        this.isLoading = false;
        console.error('Error loading city data:', error);
      },
    });
  }

  onSubmit(): void {
    if (this.cityForm.invalid) {
      return;
    }

    this.isLoading = true;
    const cityData = this.cityForm.value;

    if (this.isEditMode && this.cityId) {
      // Update existing city
      this.cityService.updateCity(this.cityId, cityData).subscribe({
        next: (response) => {
          if (response.success) {
            this.showSnackBar('City updated successfully');
            this.router.navigate(['../..'], { relativeTo: this.route });
          } else {
            this.errorMessage = response.message || 'Failed to update city';
            this.isLoading = false;
          }
        },
        error: (error) => {
          this.errorMessage =
            'Error updating city: ' + this.getErrorMessage(error);
          this.isLoading = false;
          console.error('Error updating city:', error);
        },
      });
    } else {
      // Create new city
      this.cityService.createCity(cityData).subscribe({
        next: (response) => {
          if (response.success) {
            this.showSnackBar('City created successfully');
            this.router.navigate(['..'], { relativeTo: this.route });
          } else {
            this.errorMessage = response.message || 'Failed to create city';
            this.isLoading = false;
          }
        },
        error: (error) => {
          this.errorMessage =
            'Error creating city: ' + this.getErrorMessage(error);
          this.isLoading = false;
          console.error('Error creating city:', error);
        },
      });
    }
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  getErrorMessage(error: any): string {
    return error.error?.message || error.message || 'Unknown error occurred';
  }

  // Getters for form controls
  get nameControl() {
    return this.cityForm.get('name');
  }
  get codeControl() {
    return this.cityForm.get('code');
  }
  get stateIdControl() {
    return this.cityForm.get('state_id');
  }
}
