import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSelectModule } from '@angular/material/select';
import { SelectionModel } from '@angular/cdk/collections';

import { CityService } from '../../../../core/services/masters/city.service';
import { StateService } from '../../../../core/services/masters/state.service';
import { CountryService } from '../../../../core/services/masters/country.service';
import { City } from '../../../../core/models/masters/city';
import { State } from '../../../../core/models/masters/state';
import { Country } from '../../../../core/models/masters/country';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-city-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatDialogModule,
    MatSelectModule,
  ],
  templateUrl: './city-list.component.html',
  styleUrls: ['./city-list.component.scss'],
})
export class CityListComponent implements OnInit {
  cities: City[] = [];
  filteredCities: City[] = [];
  displayedCities: City[] = []; // Cities after pagination
  displayedColumns: string[] = [
    'select',
    'id',
    'name',
    'code',
    'state_name',
    'country_name',
    'created_by',
    'status',
    'actions',
  ];
  selection = new SelectionModel<City>(true, []); // Multiple selection model
  isLoading = true;
  errorMessage = '';
  successMessage = '';
  searchTerm = '';
  includeInactive = false;

  // Filters
  states: State[] = [];
  countries: Country[] = [];
  selectedStateId: number | null = null;
  selectedCountryId: number | null = null;
  loadingStates = false;
  loadingCountries = false;

  // Pagination
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageIndex = 0;
  totalCities = 0;

  // Sorting
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private cityService: CityService,
    private stateService: StateService,
    private countryService: CountryService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadCountries();
    this.loadStates();
    this.loadCities();
  }

  loadCountries(): void {
    this.loadingCountries = true;

    this.countryService.getCountries(true).subscribe({
      next: (response) => {
        if (response.success) {
          this.countries = Array.isArray(response.data) ? response.data : [];
        }
        this.loadingCountries = false;
      },
      error: (error) => {
        console.error('Error loading countries:', error);
        this.loadingCountries = false;
      },
    });
  }

  loadStates(countryId?: number): void {
    this.loadingStates = true;
    this.states = [];

    this.stateService.getStates(true, countryId).subscribe({
      next: (response) => {
        if (response.success) {
          this.states = Array.isArray(response.data) ? response.data : [];
        }
        this.loadingStates = false;
      },
      error: (error) => {
        console.error('Error loading states:', error);
        this.loadingStates = false;
      },
    });
  }

  loadCities(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.cityService
      .getCities(
        this.includeInactive,
        this.selectedStateId || undefined,
        this.selectedCountryId || undefined
      )
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.cities = Array.isArray(response.data) ? response.data : [];
            this.totalCities = this.cities.length;
            this.applyFilter();
          } else {
            this.errorMessage = response.message || 'Failed to load cities';
          }
          this.isLoading = false;
        },
        error: (error) => {
          this.errorMessage =
            'Error loading cities: ' + this.getErrorMessage(error);
          this.isLoading = false;
          console.error('Error loading cities:', error);
        },
      });
  }

  applyFilter(): void {
    const filterValue = this.searchTerm.toLowerCase();

    this.filteredCities = this.cities.filter(
      (city) =>
        city.name.toLowerCase().includes(filterValue) ||
        city.code.toLowerCase().includes(filterValue) ||
        (city.state_name &&
          city.state_name.toLowerCase().includes(filterValue)) ||
        (city.country_name &&
          city.country_name.toLowerCase().includes(filterValue)) ||
        (city.created_by_username &&
          city.created_by_username.toLowerCase().includes(filterValue))
    );

    this.totalCities = this.filteredCities.length;

    if (this.paginator) {
      this.paginator.firstPage();
    }

    this.updateDisplayedCities();
  }

  updateDisplayedCities(): void {
    const startIndex = this.pageIndex * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.displayedCities = this.filteredCities.slice(startIndex, endIndex);
  }

  onPageChange(event: any): void {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;
    this.updateDisplayedCities();
  }

  toggleStatus(city: City): void {
    const newStatus = !city.is_active;

    this.cityService.toggleCityStatus(city.id, newStatus).subscribe({
      next: (response) => {
        if (response.success) {
          city.is_active = newStatus;
          this.showSnackBar(
            `City ${city.name} ${
              newStatus ? 'activated' : 'deactivated'
            } successfully`
          );
        } else {
          this.showSnackBar(
            `Failed to update status: ${response.message}`,
            true
          );
        }
      },
      error: (error) => {
        this.showSnackBar(
          `Error updating status: ${this.getErrorMessage(error)}`,
          true
        );
        console.error('Error toggling city status:', error);
      },
    });
  }

  deleteCity(city: City): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the city "${city.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.cityService.deleteCity(city.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.cities = this.cities.filter((c) => c.id !== city.id);
              this.applyFilter();
              this.showSnackBar(`City ${city.name} deleted successfully`);
            } else {
              this.showSnackBar(
                `Failed to delete city: ${response.message}`,
                true
              );
            }
          },
          error: (error) => {
            this.showSnackBar(
              `Error deleting city: ${this.getErrorMessage(error)}`,
              true
            );
            console.error('Error deleting city:', error);
          },
        });
      }
    });
  }

  bulkDeleteSelected(): void {
    const selectedCities = this.selection.selected;

    if (selectedCities.length === 0) {
      this.showSnackBar('No cities selected for deletion', true);
      return;
    }

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Bulk Delete',
        message: `Are you sure you want to delete ${selectedCities.length} selected cities?`,
        confirmText: 'Delete All',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const ids = selectedCities.map((city) => city.id);

        this.cityService.bulkDeleteCities(ids).subscribe({
          next: (response) => {
            if (response.success) {
              this.cities = this.cities.filter(
                (city) => !ids.includes(city.id)
              );
              this.selection.clear();
              this.applyFilter();
              this.showSnackBar(`Successfully deleted ${ids.length} cities`);
            } else {
              this.showSnackBar(
                `Failed to delete cities: ${response.message}`,
                true
              );
            }
          },
          error: (error) => {
            this.showSnackBar(
              `Error deleting cities: ${this.getErrorMessage(error)}`,
              true
            );
            console.error('Error bulk deleting cities:', error);
          },
        });
      }
    });
  }

  /** Whether the number of selected elements matches the total number of rows. */
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.displayedCities.length;
    return numSelected === numRows && numRows > 0;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  toggleAllRows(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.selection.select(...this.displayedCities);
    }
  }

  refreshList(): void {
    this.loadCities();
  }

  toggleIncludeInactive(): void {
    this.loadCities();
  }

  onCountryChange(): void {
    if (this.selectedCountryId) {
      this.loadStates(this.selectedCountryId);
      this.selectedStateId = null;
    } else {
      this.states = [];
      this.selectedStateId = null;
    }
    this.loadCities();
  }

  onStateChange(): void {
    this.loadCities();
  }

  clearCountryFilter(): void {
    this.selectedCountryId = null;
    this.states = [];
    this.selectedStateId = null;
    this.loadCities();
  }

  clearStateFilter(): void {
    this.selectedStateId = null;
    this.loadCities();
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  getErrorMessage(error: any): string {
    return error.error?.message || error.message || 'Unknown error occurred';
  }
}
