<div class="country-detail-container">
  <div class="detail-header">
    <h1>Country Details</h1>
    <button mat-button color="primary" (click)="goBack()">
      <mat-icon>arrow_back</mat-icon> Back to Countries
    </button>
  </div>

  <mat-card>
    <mat-card-content>
      <!-- Loading spinner -->
      <div class="loading-spinner" *ngIf="isLoading">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading country details...</p>
      </div>

      <!-- Error message -->
      <div class="error-message" *ngIf="errorMessage">
        <mat-icon>error</mat-icon> {{ errorMessage }}
      </div>

      <!-- Country details -->
      <div class="country-info" *ngIf="!isLoading && country">
        <div class="country-header">
          <h2>{{ country.name }}</h2>
          <mat-chip-set>
            <mat-chip [color]="country.is_active ? 'primary' : 'warn'" selected>
              {{ country.is_active ? 'Active' : 'Inactive' }}
            </mat-chip>
          </mat-chip-set>
        </div>

        <mat-divider></mat-divider>

        <div class="country-details">
          <div class="detail-item">
            <span class="label">ID:</span>
            <span class="value">{{ country.id }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Name:</span>
            <span class="value">{{ country.name }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Code:</span>
            <span class="value">{{ country.code }}</span>
          </div>
          <div class="detail-item">
            <span class="label">Status:</span>
            <span class="value">{{ country.is_active ? 'Active' : 'Inactive' }}</span>
          </div>
          <div class="detail-item" *ngIf="country.created_by_username">
            <span class="label">Created By:</span>
            <span class="value">{{ country.created_by_username }}</span>
          </div>
          <div class="detail-item" *ngIf="country.created_at">
            <span class="label">Created At:</span>
            <span class="value">{{ country.created_at | date:'medium' }}</span>
          </div>
          <div class="detail-item" *ngIf="country.updated_by_username">
            <span class="label">Updated By:</span>
            <span class="value">{{ country.updated_by_username }}</span>
          </div>
          <div class="detail-item" *ngIf="country.updated_at">
            <span class="label">Updated At:</span>
            <span class="value">{{ country.updated_at | date:'medium' }}</span>
          </div>
        </div>

        <div class="detail-actions" *ngIf="!isLoading && country">
          <button mat-raised-button color="warn" (click)="deleteCountry()">
            <mat-icon>delete</mat-icon> Delete
          </button>
          <button mat-raised-button color="accent" (click)="toggleStatus()">
            <mat-icon>{{ country.is_active ? 'toggle_off' : 'toggle_on' }}</mat-icon>
            {{ country.is_active ? 'Deactivate' : 'Activate' }}
          </button>
          <button mat-raised-button color="primary" [routerLink]="['../../edit', country.id]">
            <mat-icon>edit</mat-icon> Edit
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
