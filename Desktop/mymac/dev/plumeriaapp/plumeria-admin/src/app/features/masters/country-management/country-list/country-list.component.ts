import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatTableModule, MatTable } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { SelectionModel } from '@angular/cdk/collections';

import { CountryService } from '../../../../core/services/masters/country.service';
import { Country } from '../../../../core/models/masters/country';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-country-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatDialogModule
  ],
  templateUrl: './country-list.component.html',
  styleUrls: ['./country-list.component.scss']
})
export class CountryListComponent implements OnInit {
  countries: Country[] = [];
  filteredCountries: Country[] = [];
  displayedCountries: Country[] = []; // Countries after pagination
  displayedColumns: string[] = [
    'select',
    'id',
    'name',
    'code',
    'created_by',
    'status',
    'actions'
  ];
  selection = new SelectionModel<Country>(true, []); // Multiple selection model
  isLoading = true;
  errorMessage = '';
  successMessage = '';
  searchTerm = '';
  includeInactive = false;

  // Pagination
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageIndex = 0;
  totalCountries = 0;

  // Sorting
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private countryService: CountryService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadCountries();
  }

  loadCountries(): void {
    this.isLoading = true;
    this.errorMessage = '';
    
    this.countryService.getCountries(this.includeInactive).subscribe({
      next: (response) => {
        if (response.success) {
          this.countries = Array.isArray(response.data) ? response.data : [];
          this.totalCountries = this.countries.length;
          this.applyFilter();
        } else {
          this.errorMessage = response.message || 'Failed to load countries';
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Error loading countries: ' + this.getErrorMessage(error);
        this.isLoading = false;
        console.error('Error loading countries:', error);
      }
    });
  }

  applyFilter(): void {
    const filterValue = this.searchTerm.toLowerCase();
    
    this.filteredCountries = this.countries.filter(country => 
      country.name.toLowerCase().includes(filterValue) ||
      country.code.toLowerCase().includes(filterValue) ||
      (country.created_by_username && country.created_by_username.toLowerCase().includes(filterValue))
    );
    
    this.totalCountries = this.filteredCountries.length;
    
    if (this.paginator) {
      this.paginator.firstPage();
    }
    
    this.updateDisplayedCountries();
  }

  updateDisplayedCountries(): void {
    const startIndex = this.pageIndex * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.displayedCountries = this.filteredCountries.slice(startIndex, endIndex);
  }

  onPageChange(event: any): void {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;
    this.updateDisplayedCountries();
  }

  toggleStatus(country: Country): void {
    const newStatus = !country.is_active;
    
    this.countryService.toggleCountryStatus(country.id, newStatus).subscribe({
      next: (response) => {
        if (response.success) {
          country.is_active = newStatus;
          this.showSnackBar(`Country ${country.name} ${newStatus ? 'activated' : 'deactivated'} successfully`);
        } else {
          this.showSnackBar(`Failed to update status: ${response.message}`, true);
        }
      },
      error: (error) => {
        this.showSnackBar(`Error updating status: ${this.getErrorMessage(error)}`, true);
        console.error('Error toggling country status:', error);
      }
    });
  }

  deleteCountry(country: Country): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the country "${country.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.countryService.deleteCountry(country.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.countries = this.countries.filter(c => c.id !== country.id);
              this.applyFilter();
              this.showSnackBar(`Country ${country.name} deleted successfully`);
            } else {
              this.showSnackBar(`Failed to delete country: ${response.message}`, true);
            }
          },
          error: (error) => {
            this.showSnackBar(`Error deleting country: ${this.getErrorMessage(error)}`, true);
            console.error('Error deleting country:', error);
          }
        });
      }
    });
  }

  bulkDeleteSelected(): void {
    const selectedCountries = this.selection.selected;
    
    if (selectedCountries.length === 0) {
      this.showSnackBar('No countries selected for deletion', true);
      return;
    }

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: 'Confirm Bulk Delete',
        message: `Are you sure you want to delete ${selectedCountries.length} selected countries?`,
        confirmText: 'Delete All',
        cancelText: 'Cancel'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        const ids = selectedCountries.map(country => country.id);
        
        this.countryService.bulkDeleteCountries(ids).subscribe({
          next: (response) => {
            if (response.success) {
              this.countries = this.countries.filter(country => !ids.includes(country.id));
              this.selection.clear();
              this.applyFilter();
              this.showSnackBar(`Successfully deleted ${ids.length} countries`);
            } else {
              this.showSnackBar(`Failed to delete countries: ${response.message}`, true);
            }
          },
          error: (error) => {
            this.showSnackBar(`Error deleting countries: ${this.getErrorMessage(error)}`, true);
            console.error('Error bulk deleting countries:', error);
          }
        });
      }
    });
  }

  /** Whether the number of selected elements matches the total number of rows. */
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.displayedCountries.length;
    return numSelected === numRows && numRows > 0;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  toggleAllRows(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.selection.select(...this.displayedCountries);
    }
  }

  refreshList(): void {
    this.loadCountries();
  }

  toggleIncludeInactive(): void {
    this.includeInactive = !this.includeInactive;
    this.loadCountries();
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }

  getErrorMessage(error: any): string {
    return error.error?.message || error.message || 'Unknown error occurred';
  }
}
