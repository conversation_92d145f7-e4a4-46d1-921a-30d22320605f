<div class="department-form-container">
  <div class="form-header">
    <h1>{{ pageTitle }}</h1>
    <button mat-raised-button color="primary" routerLink="../">
      <mat-icon>arrow_back</mat-icon> Back to List
    </button>
  </div>

  <mat-card>
    <mat-card-content>
      <!-- Loading spinner -->
      <div class="loading-spinner" *ngIf="isLoading">
        <mat-spinner diameter="40"></mat-spinner>
        <p>Loading department data...</p>
      </div>

      <!-- Error message -->
      <div class="error-message" *ngIf="errorMessage">
        <mat-icon>error</mat-icon> {{ errorMessage }}
      </div>

      <!-- Department form -->
      <form [formGroup]="departmentForm" (ngSubmit)="onSubmit()" *ngIf="!isLoading">
        <div class="form-row">
          <div class="form-field">
            <mat-form-field appearance="outline">
              <mat-label>Department Name</mat-label>
              <input matInput formControlName="name" placeholder="Enter department name">
              <mat-error *ngIf="nameControl?.invalid && nameControl?.touched">
                <span *ngIf="nameControl?.errors?.['required']">Name is required</span>
                <span *ngIf="nameControl?.errors?.['maxlength']">Name cannot exceed 100 characters</span>
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-field">
            <mat-form-field appearance="outline">
              <mat-label>Department Code</mat-label>
              <input matInput formControlName="code" placeholder="Enter department code">
              <mat-error *ngIf="codeControl?.invalid && codeControl?.touched">
                <span *ngIf="codeControl?.errors?.['required']">Code is required</span>
                <span *ngIf="codeControl?.errors?.['maxlength']">Code cannot exceed 20 characters</span>
              </mat-error>
            </mat-form-field>
          </div>
        </div>

        <div class="form-row">
          <div class="form-field status-toggle">
            <mat-slide-toggle formControlName="is_active" color="primary">
              Active
            </mat-slide-toggle>
            <div class="toggle-hint">
              Toggle to set the department as active or inactive
            </div>
          </div>
        </div>

        <div class="form-actions">
          <button mat-button type="button" routerLink="../" [disabled]="isSubmitting">
            Cancel
          </button>
          <button mat-raised-button color="primary" type="submit" [disabled]="isSubmitting">
            <mat-icon *ngIf="isSubmitting">
              <mat-spinner diameter="20" color="accent"></mat-spinner>
            </mat-icon>
            <span *ngIf="!isSubmitting">{{ submitButtonText }}</span>
            <span *ngIf="isSubmitting">Saving...</span>
          </button>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>
