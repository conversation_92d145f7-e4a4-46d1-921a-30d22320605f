import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { DesignationService } from '../../../../core/services/masters/designation.service';
import { Designation } from '../../../../core/models/masters/designation';

@Component({
  selector: 'app-designation-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatSlideToggleModule,
    MatSnackBarModule,
  ],
  templateUrl: './designation-form.component.html',
  styleUrls: ['./designation-form.component.scss'],
})
export class DesignationFormComponent implements OnInit {
  designationForm!: FormGroup;
  isEditMode = false;
  designationId: number | null = null;
  isLoading = false;
  isSubmitting = false;
  errorMessage = '';
  pageTitle = 'Add New Designation';
  submitButtonText = 'Create Designation';

  constructor(
    private fb: FormBuilder,
    private designationService: DesignationService,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    this.createForm();
  }

  ngOnInit(): void {
    // Check if we're in edit mode
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.isEditMode = true;
        this.designationId = +id;
        this.pageTitle = 'Edit Designation';
        this.submitButtonText = 'Update Designation';
        this.loadDesignation(+id);
      }
    });
  }

  createForm(): void {
    this.designationForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      code: ['', [Validators.required, Validators.maxLength(20)]],
      is_active: [true]
    });
  }

  loadDesignation(id: number): void {
    this.isLoading = true;
    this.designationService.getDesignationById(id).subscribe({
      next: (response) => {
        if (response.success && !Array.isArray(response.data)) {
          const designation = response.data;
          this.designationForm.patchValue({
            name: designation.name,
            code: designation.code,
            is_active: designation.is_active
          });
        } else {
          this.errorMessage = 'Failed to load designation details';
          this.showSnackBar(this.errorMessage, true);
          this.router.navigate(['../'], { relativeTo: this.route });
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Error loading designation: ' + this.getErrorMessage(error);
        this.showSnackBar(this.errorMessage, true);
        this.isLoading = false;
        this.router.navigate(['../'], { relativeTo: this.route });
      }
    });
  }

  onSubmit(): void {
    if (this.designationForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.designationForm.controls).forEach(key => {
        const control = this.designationForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;
    const designationData: Partial<Designation> = this.designationForm.value;

    if (this.isEditMode && this.designationId) {
      // Update existing designation
      this.designationService.updateDesignation(this.designationId, designationData).subscribe({
        next: (response) => {
          if (response.success) {
            this.showSnackBar('Designation updated successfully');
            this.router.navigate(['../../'], { relativeTo: this.route });
          } else {
            this.errorMessage = response.message || 'Failed to update designation';
            this.showSnackBar(this.errorMessage, true);
          }
          this.isSubmitting = false;
        },
        error: (error) => {
          this.errorMessage = 'Error updating designation: ' + this.getErrorMessage(error);
          this.showSnackBar(this.errorMessage, true);
          this.isSubmitting = false;
        }
      });
    } else {
      // Create new designation
      this.designationService.createDesignation(designationData).subscribe({
        next: (response) => {
          if (response.success) {
            this.showSnackBar('Designation created successfully');
            this.router.navigate(['../'], { relativeTo: this.route });
          } else {
            this.errorMessage = response.message || 'Failed to create designation';
            this.showSnackBar(this.errorMessage, true);
          }
          this.isSubmitting = false;
        },
        error: (error) => {
          this.errorMessage = 'Error creating designation: ' + this.getErrorMessage(error);
          this.showSnackBar(this.errorMessage, true);
          this.isSubmitting = false;
        }
      });
    }
  }

  getErrorMessage(error: any): string {
    return error.error?.message || error.message || 'Unknown error';
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  // Form validation helpers
  get nameControl() { return this.designationForm.get('name'); }
  get codeControl() { return this.designationForm.get('code'); }
}
