import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule, MatSort } from '@angular/material/sort';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { SelectionModel } from '@angular/cdk/collections';

import { DesignationService } from '../../../../core/services/masters/designation.service';
import { Designation } from '../../../../core/models/masters/designation';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-designation-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatInputModule,
    MatFormFieldModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatCheckboxModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatDialogModule,
  ],
  templateUrl: './designation-list.component.html',
  styleUrls: ['./designation-list.component.scss'],
})
export class DesignationListComponent implements OnInit {
  designations: Designation[] = [];
  filteredDesignations: Designation[] = [];
  displayedDesignations: Designation[] = []; // Designations after pagination
  displayedColumns: string[] = [
    'select',
    'id',
    'name',
    'code',
    'created_by',
    'status',
    'actions',
  ];
  selection = new SelectionModel<Designation>(true, []); // Multiple selection model
  isLoading = true;
  errorMessage = '';
  successMessage = '';
  searchTerm = '';
  includeInactive = false;

  // Pagination
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageIndex = 0;
  totalDesignations = 0;

  // Sorting
  @ViewChild(MatSort) sort!: MatSort;

  constructor(
    private designationService: DesignationService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadDesignations();
  }

  loadDesignations(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.designationService
      .getDesignations(this.includeInactive)
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.designations = Array.isArray(response.data) ? response.data : [];
            this.totalDesignations = this.designations.length;
            this.applyFilter();
          } else {
            this.errorMessage = response.message || 'Failed to load designations';
          }
          this.isLoading = false;
        },
        error: (error) => {
          this.errorMessage =
            'Error loading designations: ' + this.getErrorMessage(error);
          this.isLoading = false;
          console.error('Error loading designations:', error);
        },
      });
  }

  applyFilter(): void {
    // Filter designations based on search term
    const searchTermLower = this.searchTerm.toLowerCase().trim();
    
    this.filteredDesignations = this.designations.filter((designation) => {
      return (
        designation.name.toLowerCase().includes(searchTermLower) ||
        designation.code.toLowerCase().includes(searchTermLower)
      );
    });

    this.totalDesignations = this.filteredDesignations.length;
    this.selection.clear();
    this.updateDisplayedDesignations();
  }

  updateDisplayedDesignations(): void {
    const startIndex = this.pageIndex * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.displayedDesignations = this.filteredDesignations.slice(startIndex, endIndex);
  }

  onPageChange(event: any): void {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;
    this.updateDisplayedDesignations();
  }

  deleteDesignation(designation: Designation): void {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the designation "${designation.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.designationService.deleteDesignation(designation.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.successMessage = 'Designation deleted successfully';
              this.showSnackBar(this.successMessage);
              this.loadDesignations();
            } else {
              this.errorMessage = response.message || 'Failed to delete designation';
              this.showSnackBar(this.errorMessage, true);
            }
          },
          error: (error) => {
            this.errorMessage = 'Error deleting designation: ' + this.getErrorMessage(error);
            this.showSnackBar(this.errorMessage, true);
            console.error('Error deleting designation:', error);
          },
        });
      }
    });
  }

  bulkDeleteSelected(): void {
    if (this.selection.selected.length === 0) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirm Bulk Delete',
        message: `Are you sure you want to delete ${this.selection.selected.length} selected designations?`,
        confirmText: 'Delete All',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        const ids = this.selection.selected.map((designation) => designation.id);
        
        this.designationService.bulkDeleteDesignations(ids).subscribe({
          next: (response) => {
            if (response.success) {
              this.successMessage = 'Designations deleted successfully';
              this.showSnackBar(this.successMessage);
              this.selection.clear();
              this.loadDesignations();
            } else {
              this.errorMessage = response.message || 'Failed to delete designations';
              this.showSnackBar(this.errorMessage, true);
            }
          },
          error: (error) => {
            this.errorMessage = 'Error deleting designations: ' + this.getErrorMessage(error);
            this.showSnackBar(this.errorMessage, true);
            console.error('Error deleting designations:', error);
          },
        });
      }
    });
  }

  toggleStatus(designation: Designation): void {
    const newStatus = !designation.is_active;
    
    this.designationService.toggleDesignationStatus(designation.id, newStatus).subscribe({
      next: (response) => {
        if (response.success) {
          designation.is_active = newStatus;
          this.successMessage = `Designation ${newStatus ? 'activated' : 'deactivated'} successfully`;
          this.showSnackBar(this.successMessage);
        } else {
          this.errorMessage = response.message || 'Failed to update designation status';
          this.showSnackBar(this.errorMessage, true);
        }
      },
      error: (error) => {
        this.errorMessage = 'Error updating designation status: ' + this.getErrorMessage(error);
        this.showSnackBar(this.errorMessage, true);
        console.error('Error updating designation status:', error);
      },
    });
  }

  getErrorMessage(error: any): string {
    return error.error?.message || error.message || 'Unknown error';
  }

  /** Whether the number of selected elements matches the total number of rows. */
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.displayedDesignations.length;
    return numSelected === numRows && numRows > 0;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  toggleAllRows(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.selection.select(...this.displayedDesignations);
    }
  }

  refreshList(): void {
    this.loadDesignations();
  }

  toggleIncludeInactive(): void {
    this.loadDesignations();
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }
}
