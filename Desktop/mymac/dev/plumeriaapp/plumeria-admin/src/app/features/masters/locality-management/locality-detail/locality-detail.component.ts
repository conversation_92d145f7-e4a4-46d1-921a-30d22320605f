import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { LocalityService } from '../../../../core/services/masters/locality.service';
import { Locality } from '../../../../core/models/masters/locality';
import { ConfirmDialogComponent } from '../../../../shared/components/confirm-dialog/confirm-dialog.component';

@Component({
  selector: 'app-locality-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatTooltipModule,
    MatDialogModule,
    MatSnackBarModule,
  ],
  templateUrl: './locality-detail.component.html',
  styleUrls: ['./locality-detail.component.scss'],
})
export class LocalityDetailComponent implements OnInit {
  locality: Locality | null = null;
  isLoading = true;
  errorMessage = '';

  constructor(
    private localityService: LocalityService,
    private route: ActivatedRoute,
    private router: Router,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.loadLocality(+id);
      } else {
        this.router.navigate(['../'], { relativeTo: this.route });
      }
    });
  }

  loadLocality(id: number): void {
    this.isLoading = true;
    this.localityService.getLocalityById(id).subscribe({
      next: (response) => {
        if (response.success && !Array.isArray(response.data)) {
          this.locality = response.data;
        } else {
          this.errorMessage = response.message || 'Failed to load locality details';
          this.showSnackBar(this.errorMessage, true);
          this.router.navigate(['../'], { relativeTo: this.route });
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Error loading locality: ' + this.getErrorMessage(error);
        this.showSnackBar(this.errorMessage, true);
        this.isLoading = false;
        this.router.navigate(['../'], { relativeTo: this.route });
      }
    });
  }

  deleteLocality(): void {
    if (!this.locality) return;

    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirm Delete',
        message: `Are you sure you want to delete the locality "${this.locality.name}"?`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && this.locality) {
        this.localityService.deleteLocality(this.locality.id).subscribe({
          next: (response) => {
            if (response.success) {
              this.showSnackBar('Locality deleted successfully');
              this.router.navigate(['../../'], { relativeTo: this.route });
            } else {
              this.errorMessage = response.message || 'Failed to delete locality';
              this.showSnackBar(this.errorMessage, true);
            }
          },
          error: (error) => {
            this.errorMessage = 'Error deleting locality: ' + this.getErrorMessage(error);
            this.showSnackBar(this.errorMessage, true);
          }
        });
      }
    });
  }

  toggleStatus(): void {
    if (!this.locality) return;

    const newStatus = !this.locality.is_active;
    
    this.localityService.toggleLocalityStatus(this.locality.id, newStatus).subscribe({
      next: (response) => {
        if (response.success) {
          this.locality!.is_active = newStatus;
          this.showSnackBar(`Locality ${newStatus ? 'activated' : 'deactivated'} successfully`);
        } else {
          this.errorMessage = response.message || 'Failed to update locality status';
          this.showSnackBar(this.errorMessage, true);
        }
      },
      error: (error) => {
        this.errorMessage = 'Error updating locality status: ' + this.getErrorMessage(error);
        this.showSnackBar(this.errorMessage, true);
      }
    });
  }

  getErrorMessage(error: any): string {
    return error.error?.message || error.message || 'Unknown error';
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top',
    });
  }

  getStatusClass(): string {
    return this.locality?.is_active ? 'status-active' : 'status-inactive';
  }

  getStatusText(): string {
    return this.locality?.is_active ? 'Active' : 'Inactive';
  }

  getFormattedDate(dateString?: string): string {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  }
}
