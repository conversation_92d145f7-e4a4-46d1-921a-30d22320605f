@import 'styles/variables';
@import 'styles/mixins';

.locality-form-container {
  @include card-container;

  .form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $spacing-lg;

    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 500;
      color: $text-color;
    }
  }

  mat-card {
    @include card-styling;
    background-color: $card-background;
  }

  .loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl 0;

    p {
      margin-top: $spacing-md;
      color: $text-secondary;
    }
  }

  .error-message {
    @include message-box($error-color);
  }

  form {
    display: flex;
    flex-direction: column;
    gap: $spacing-lg;

    .form-row {
      display: flex;
      gap: $spacing-lg;
      flex-wrap: wrap;

      .form-field {
        flex: 1;
        min-width: 250px;

        mat-form-field {
          width: 100%;
        }

        &.status-toggle {
          display: flex;
          flex-direction: column;
          padding-top: $spacing-sm;

          .toggle-hint {
            font-size: 12px;
            color: $text-secondary;
            margin-top: 4px;
          }
        }
      }
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: $spacing-md;
      margin-top: $spacing-lg;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .locality-form-container {
    .form-header {
      flex-direction: column;
      align-items: flex-start;
      gap: $spacing-md;

      button {
        align-self: flex-start;
      }
    }

    form {
      .form-row {
        flex-direction: column;
        gap: $spacing-md;

        .form-field {
          width: 100%;
        }
      }
    }
  }
}
