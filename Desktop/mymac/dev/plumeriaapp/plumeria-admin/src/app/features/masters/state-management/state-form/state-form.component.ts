import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatSelectModule } from '@angular/material/select';

import { StateService } from '../../../../core/services/masters/state.service';
import { CountryService } from '../../../../core/services/masters/country.service';
import { State } from '../../../../core/models/masters/state';
import { Country } from '../../../../core/models/masters/country';

@Component({
  selector: 'app-state-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSlideToggleModule,
    MatSnackBarModule,
    MatSelectModule
  ],
  templateUrl: './state-form.component.html',
  styleUrls: ['./state-form.component.scss']
})
export class StateFormComponent implements OnInit {
  stateForm!: FormGroup;
  isEditMode = false;
  stateId: number | null = null;
  isLoading = false;
  errorMessage = '';
  countries: Country[] = [];
  loadingCountries = false;

  constructor(
    private fb: FormBuilder,
    private stateService: StateService,
    private countryService: CountryService,
    private route: ActivatedRoute,
    private router: Router,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.loadCountries();
    
    // Check if we're in edit mode
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.isEditMode = true;
        this.stateId = +id;
        this.loadStateData(+id);
      }
    });
  }

  initForm(): void {
    this.stateForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      code: ['', [Validators.required, Validators.minLength(1), Validators.maxLength(10)]],
      country_id: ['', [Validators.required]],
      is_active: [true]
    });
  }

  loadCountries(): void {
    this.loadingCountries = true;
    
    this.countryService.getCountries(true).subscribe({
      next: (response) => {
        if (response.success) {
          this.countries = Array.isArray(response.data) ? response.data : [];
        } else {
          this.errorMessage = response.message || 'Failed to load countries';
        }
        this.loadingCountries = false;
      },
      error: (error) => {
        this.errorMessage = 'Error loading countries: ' + this.getErrorMessage(error);
        this.loadingCountries = false;
        console.error('Error loading countries:', error);
      }
    });
  }

  loadStateData(id: number): void {
    this.isLoading = true;
    
    this.stateService.getStateById(id).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          const state = response.data as State;
          this.stateForm.patchValue({
            name: state.name,
            code: state.code,
            country_id: state.country_id,
            is_active: state.is_active
          });
        } else {
          this.errorMessage = response.message || 'Failed to load state data';
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Error loading state data: ' + this.getErrorMessage(error);
        this.isLoading = false;
        console.error('Error loading state data:', error);
      }
    });
  }

  onSubmit(): void {
    if (this.stateForm.invalid) {
      return;
    }

    this.isLoading = true;
    const stateData = this.stateForm.value;

    if (this.isEditMode && this.stateId) {
      // Update existing state
      this.stateService.updateState(this.stateId, stateData).subscribe({
        next: (response) => {
          if (response.success) {
            this.showSnackBar('State updated successfully');
            this.router.navigate(['../..'], { relativeTo: this.route });
          } else {
            this.errorMessage = response.message || 'Failed to update state';
            this.isLoading = false;
          }
        },
        error: (error) => {
          this.errorMessage = 'Error updating state: ' + this.getErrorMessage(error);
          this.isLoading = false;
          console.error('Error updating state:', error);
        }
      });
    } else {
      // Create new state
      this.stateService.createState(stateData).subscribe({
        next: (response) => {
          if (response.success) {
            this.showSnackBar('State created successfully');
            this.router.navigate(['..'], { relativeTo: this.route });
          } else {
            this.errorMessage = response.message || 'Failed to create state';
            this.isLoading = false;
          }
        },
        error: (error) => {
          this.errorMessage = 'Error creating state: ' + this.getErrorMessage(error);
          this.isLoading = false;
          console.error('Error creating state:', error);
        }
      });
    }
  }

  showSnackBar(message: string, isError = false): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: isError ? 'error-snackbar' : 'success-snackbar',
      horizontalPosition: 'end',
      verticalPosition: 'top'
    });
  }

  getErrorMessage(error: any): string {
    return error.error?.message || error.message || 'Unknown error occurred';
  }

  // Getters for form controls
  get nameControl() { return this.stateForm.get('name'); }
  get codeControl() { return this.stateForm.get('code'); }
  get countryIdControl() { return this.stateForm.get('country_id'); }
}
