// src/app/features/module-management/module-detail/module-detail.component.ts

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { Module } from '../../../core/models/module';
import { ModuleService } from '../../../core/services/module.service';

@Component({
  selector: 'app-module-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatDividerModule,
  ],
  templateUrl: './module-detail.component.html',
  styleUrl: './module-detail.component.scss',
})
export class ModuleDetailComponent implements OnInit {
  module: Module | null = null;
  isLoading = true;
  errorMessage = '';

  constructor(
    private moduleService: ModuleService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.loadModuleData(+params['id']);
      }
    });
  }

  loadModuleData(id: number): void {
    this.isLoading = true;
    this.moduleService.getModuleById(id).subscribe({
      next: (response) => {
        this.module = response.data;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Error loading module: ' + error.message;
        this.isLoading = false;
      },
    });
  }

  editModule(): void {
    if (this.module) {
      this.router.navigate(['/admin/modules/edit', this.module.id]);
    }
  }

  goBack(): void {
    this.router.navigate(['/admin/modules']);
  }

  toggleModuleStatus(): void {
    if (!this.module) return;

    this.isLoading = true;
    this.moduleService
      .toggleModuleStatus(this.module.id, !this.module.is_active)
      .subscribe({
        next: (response) => {
          this.module = response.data;
          this.isLoading = false;
        },
        error: (error) => {
          this.errorMessage = 'Error toggling module status: ' + error.message;
          this.isLoading = false;
        },
      });
  }

  deleteModule(): void {
    if (!this.module) return;

    if (confirm('Are you sure you want to delete this module?')) {
      this.isLoading = true;
      this.moduleService.deleteModule(this.module.id).subscribe({
        next: () => {
          this.isLoading = false;
          this.router.navigate(['/admin/modules']);
        },
        error: (error) => {
          this.errorMessage = 'Error deleting module: ' + error.message;
          this.isLoading = false;
        },
      });
    }
  }
}
