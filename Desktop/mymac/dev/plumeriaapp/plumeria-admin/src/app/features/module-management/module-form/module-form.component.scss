.module-form-container {
  padding: 20px;

  mat-card {
    margin-bottom: 20px;
  }

  .loading-spinner {
    display: flex;
    justify-content: center;
    margin: 20px 0;
  }

  .error-message {
    color: #d32f2f;
    margin: 15px 0;
    padding: 15px;
    background-color: #ffebee;
    border-radius: 4px;
    border-left: 4px solid #d32f2f;
    font-size: 14px;
    line-height: 1.5;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    mat-icon {
      vertical-align: middle;
      margin-right: 8px;
    }

    p {
      margin: 10px 0 5px;
      font-weight: 500;
    }

    ul {
      margin: 5px 0;
      padding-left: 25px;
    }

    .error-actions {
      margin-top: 15px;
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }

    a {
      color: #1976d2;
      text-decoration: none;
      font-weight: 500;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .form-row {
    margin-bottom: 20px;
    width: 100%;

    mat-form-field {
      width: 100%;
    }
  }

  mat-card-actions {
    padding: 16px;
  }
}
