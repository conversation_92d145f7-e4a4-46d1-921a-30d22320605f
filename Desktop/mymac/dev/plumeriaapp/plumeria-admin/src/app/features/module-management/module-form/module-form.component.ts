// src/app/features/module-management/module-form/module-form.component.ts

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { ModuleService } from '../../../core/services/module.service';
import { Module } from '../../../core/models/module';

@Component({
  selector: 'app-module-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCardModule,
    MatSlideToggleModule,
    MatIconModule,
    MatProgressSpinnerModule,
    RouterModule,
  ],
  templateUrl: './module-form.component.html',
  styleUrl: './module-form.component.scss',
})
export class ModuleFormComponent implements OnInit {
  moduleForm!: FormGroup;
  isEditMode = false;
  moduleId: number | null = null;
  isLoading = false;
  errorMessage = '';

  constructor(
    private fb: FormBuilder,
    private moduleService: ModuleService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.createForm();

    // Check if we're in edit mode
    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.moduleId = +params['id'];
        this.isEditMode = true;
        this.loadModuleData();
      }
    });
  }

  createForm(): void {
    this.moduleForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      description: [''],
      is_active: [true],
    });
  }

  loadModuleData(): void {
    if (!this.moduleId) return;

    this.isLoading = true;
    this.errorMessage = ''; // Clear any previous error messages

    console.log(`Loading module data for ID: ${this.moduleId}`);

    this.moduleService.getModuleById(this.moduleId).subscribe({
      next: (response) => {
        if (!response || !response.data) {
          this.errorMessage = 'Error: Received empty response from server';
          this.isLoading = false;
          return;
        }

        const module = response.data;
        console.log('Loaded module data:', module);

        // Update form values
        this.moduleForm.patchValue({
          name: module.name || '',
          description: module.description || '',
          is_active: module.is_active !== undefined ? module.is_active : true,
        });

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Module loading error:', error);
        this.errorMessage = error.message || 'Error loading module data';
        this.isLoading = false;

        // If there's a network error, provide a more helpful message
        if (error.status === 0) {
          this.errorMessage =
            'Cannot connect to the server. Please check your network connection.';
        } else if (error.status === 500) {
          this.errorMessage =
            'Server error occurred. Please try again later or contact support.';
        }
      },
    });
  }

  onSubmit(): void {
    if (this.moduleForm.invalid) {
      // Mark all fields as touched to trigger validation visuals
      Object.keys(this.moduleForm.controls).forEach((key) => {
        const control = this.moduleForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isLoading = true;
    const moduleData = { ...this.moduleForm.value };

    console.log('Submitting module data:', moduleData);

    if (this.isEditMode && this.moduleId) {
      // Update existing module
      console.log(`Updating module with ID: ${this.moduleId}`, moduleData);
      this.moduleService.updateModule(this.moduleId, moduleData).subscribe({
        next: (response) => {
          console.log('Module update response:', response);
          this.isLoading = false;
          this.router.navigate(['/admin/modules']);
        },
        error: (error) => {
          console.error('Module update error:', error);
          this.errorMessage = error.message || 'Error updating module';
          this.isLoading = false;

          // If there's a network error, provide a more helpful message
          if (error.status === 0) {
            this.errorMessage =
              'Cannot connect to the server. Please check your network connection.';
          } else if (error.status === 500) {
            this.errorMessage =
              'Server error occurred while updating. Please try again later or contact support.';
          } else if (error.status === 400) {
            this.errorMessage =
              'Invalid data provided. Please check your inputs and try again.';
          } else if (error.status === 409) {
            this.errorMessage =
              'A module with this name already exists. Please use a different name.';
          }
        },
      });
    } else {
      // Create new module
      console.log('Creating new module:', moduleData);
      this.moduleService.createModule(moduleData).subscribe({
        next: (response) => {
          console.log('Module creation response:', response);
          this.isLoading = false;
          this.router.navigate(['/admin/modules']);
        },
        error: (error) => {
          console.error('Module creation error:', error);
          this.errorMessage = error.message || 'Error creating module';
          this.isLoading = false;

          // If there's a network error, provide a more helpful message
          if (error.status === 0) {
            this.errorMessage =
              'Cannot connect to the server. Please check your network connection.';
          } else if (error.status === 500) {
            this.errorMessage =
              'Server error occurred while creating. Please try again later or contact support.';
          } else if (error.status === 400) {
            this.errorMessage =
              'Invalid data provided. Please check your inputs and try again.';
          } else if (error.status === 409) {
            this.errorMessage =
              'A module with this name already exists. Please use a different name.';
          }
        },
      });
    }
  }

  cancel(): void {
    this.router.navigate(['/admin/modules']);
  }

  retryLoading(): void {
    this.errorMessage = '';
    this.isLoading = true;

    if (this.moduleId) {
      console.log('Retrying to load module data...');
      this.loadModuleData();
    }
  }
}
