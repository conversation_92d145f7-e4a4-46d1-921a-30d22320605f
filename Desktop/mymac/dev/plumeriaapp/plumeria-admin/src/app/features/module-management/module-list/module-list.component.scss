.module-list-container {
  padding: 20px;

  .loading-spinner {
    display: flex;
    justify-content: center;
    margin: 20px 0;
  }

  .error-message {
    color: red;
    margin: 10px 0;
    padding: 10px;
    background-color: #ffeeee;
    border-radius: 4px;
  }

  .success-message {
    color: green;
    margin: 10px 0;
    padding: 10px;
    background-color: #eeffee;
    border-radius: 4px;
  }

  .filter-container {
    margin-bottom: 20px;

    mat-form-field {
      width: 100%;
    }
  }

  .action-buttons {
    margin: 20px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left-actions, .right-actions {
      display: flex;
      align-items: center;
    }

    button {
      margin-left: 10px;
    }
  }

  table {
    width: 100%;
    margin-top: 20px;

    .mat-column-select {
      width: 60px;
      padding-right: 8px;
    }
  }

  .status-active {
    color: green;
    font-weight: 500;
  }

  .status-inactive {
    color: red;
    font-weight: 500;
  }

  .no-data {
    text-align: center;
    margin: 20px 0;
    color: #666;
  }

  mat-paginator {
    margin-top: 20px;
  }
}
