// src/app/features/module-management/module-list/module-list.component.ts

import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, MatPaginator } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { SelectionModel } from '@angular/cdk/collections';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { Module } from '../../../core/models/module';
import { ModuleService } from '../../../core/services/module.service';

@Component({
  selector: 'app-module-list',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    MatProgressSpinnerModule,
    MatInputModule,
    MatFormFieldModule,
    FormsModule,
    MatCheckboxModule,
    MatDialogModule,
  ],
  templateUrl: './module-list.component.html',
  styleUrl: './module-list.component.scss',
})
export class ModuleListComponent implements OnInit {
  modules: Module[] = [];
  filteredModules: Module[] = [];
  displayedModules: Module[] = []; // Modules after pagination
  displayedColumns: string[] = [
    'select',
    'id',
    'name',
    'description',
    'created_by',
    'status',
    'actions',
  ];
  selection = new SelectionModel<Module>(true, []); // Multiple selection model
  isLoading = true;
  errorMessage = '';
  successMessage = '';
  searchTerm = '';

  // Pagination
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  pageSize = 5;
  pageSizeOptions: number[] = [5, 10, 25, 50];
  pageIndex = 0;
  totalModules = 0;

  constructor(
    private moduleService: ModuleService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadModules();
  }

  loadModules(): void {
    this.isLoading = true;
    this.moduleService.getModules(true).subscribe({
      next: (response) => {
        this.modules = response.data;
        this.applyFilter();
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Error loading modules: ' + error.message;
        this.isLoading = false;
      },
    });
  }

  applyFilter(): void {
    if (!this.searchTerm) {
      this.filteredModules = [...this.modules];
    } else {
      const searchTermLower = this.searchTerm.toLowerCase();
      this.filteredModules = this.modules.filter(
        (module) =>
          module.name.toLowerCase().includes(searchTermLower) ||
          (module.description &&
            module.description.toLowerCase().includes(searchTermLower)) ||
          (module.created_by_username &&
            module.created_by_username.toLowerCase().includes(searchTermLower))
      );
    }
    this.totalModules = this.filteredModules.length;
    this.selection.clear(); // Clear selection when filter is applied
    this.updateDisplayedModules();
  }

  updateDisplayedModules(): void {
    const startIndex = this.pageIndex * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.displayedModules = this.filteredModules.slice(startIndex, endIndex);
  }

  onPageChange(event: any): void {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;
    this.selection.clear(); // Clear selection when page changes
    this.updateDisplayedModules();
  }

  toggleModuleStatus(id: number, currentStatus: boolean): void {
    this.moduleService.toggleModuleStatus(id, !currentStatus).subscribe({
      next: () => {
        this.loadModules(); // Reload modules after status change
      },
      error: (error) => {
        this.errorMessage = 'Error toggling module status: ' + error.message;
      },
    });
  }

  deleteModule(id: number): void {
    if (confirm('Are you sure you want to delete this module?')) {
      this.moduleService.deleteModule(id).subscribe({
        next: () => {
          this.modules = this.modules.filter((module) => module.id !== id);
          this.selection.clear(); // Clear selection after deletion
          this.applyFilter(); // This will update filteredModules and displayedModules
        },
        error: (error) => {
          this.errorMessage = 'Error deleting module: ' + error.message;
        },
      });
    }
  }

  /** Whether the number of selected elements matches the total number of rows. */
  isAllSelected(): boolean {
    const numSelected = this.selection.selected.length;
    const numRows = this.displayedModules.length;
    return numSelected === numRows;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  toggleAllRows(): void {
    if (this.isAllSelected()) {
      this.selection.clear();
    } else {
      this.selection.select(...this.displayedModules);
    }
  }

  /** The label for the checkbox on the passed row */
  checkboxLabel(row?: Module): string {
    if (!row) {
      return `${this.isAllSelected() ? 'deselect' : 'select'} all`;
    }
    return `${this.selection.isSelected(row) ? 'deselect' : 'select'} row ${
      row.id
    }`;
  }

  /** Get selected module IDs */
  getSelectedModuleIds(): number[] {
    return this.selection.selected.map((module) => module.id);
  }

  /** Delete selected modules */
  deleteSelectedModules(): void {
    const selectedIds = this.getSelectedModuleIds();

    if (selectedIds.length === 0) {
      return; // No modules selected
    }

    if (
      confirm(
        `Are you sure you want to delete ${selectedIds.length} selected module(s)?`
      )
    ) {
      this.isLoading = true;
      this.errorMessage = ''; // Clear any previous error messages

      this.moduleService.bulkDeleteModules(selectedIds).subscribe({
        next: (response) => {
          // Remove deleted modules from the list
          this.modules = this.modules.filter(
            (module) => !selectedIds.includes(module.id)
          );
          this.selection.clear(); // Clear selection after deletion
          this.applyFilter(); // Update displayed modules
          this.isLoading = false;

          // Show success message
          const count = response.data?.count || selectedIds.length;
          this.showSuccessMessage(`Successfully deleted ${count} module(s)`);
        },
        error: (error) => {
          this.errorMessage = 'Error deleting modules: ' + error.message;
          this.isLoading = false;
        },
      });
    }
  }

  /** Show success message */
  private showSuccessMessage(message: string): void {
    // Store success message in a property that can be displayed in the template
    this.successMessage = message;

    // Clear the success message after 5 seconds
    setTimeout(() => {
      this.successMessage = '';
    }, 5000);
  }
}
