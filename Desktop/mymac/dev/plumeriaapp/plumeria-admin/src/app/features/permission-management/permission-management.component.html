<div class="permission-management-container">
  <div class="page-header">
    <h1>Permission Management</h1>
    <p class="description">Manage permissions for roles across different modules</p>
  </div>

  <div *ngIf="errorMessage" class="error-message">
    <mat-icon>error</mat-icon>
    <span>{{ errorMessage }}</span>
    <button mat-icon-button (click)="clearError()" matTooltip="Dismiss">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <mat-card class="selection-card">
    <mat-card-content>
      <div class="selection-container">
        <div class="role-selection">
          <mat-form-field appearance="outline">
            <mat-label>Select Role</mat-label>
            <mat-select [(value)]="selectedRole" (selectionChange)="onRoleSelect($event.value)" [disabled]="isLoading">
              <mat-option *ngFor="let role of roles" [value]="role">
                {{ role.name }}
              </mat-option>
            </mat-select>
            <mat-hint>Select a role to manage its permissions</mat-hint>
          </mat-form-field>
        </div>

        <div class="module-selection" [class.disabled]="!selectedRole">
          <mat-form-field appearance="outline">
            <mat-label>Select Module</mat-label>
            <mat-select [(value)]="selectedModule" (selectionChange)="onModuleSelect($event.value)" [disabled]="!selectedRole || isLoading">
              <mat-option *ngFor="let module of modules" [value]="module">
                {{ module.name }}
              </mat-option>
            </mat-select>
            <mat-hint>Select a module to manage permissions for</mat-hint>
          </mat-form-field>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
    <span>Loading permissions...</span>
  </div>

  <ng-container *ngIf="selectedRole && selectedModule && !isLoading">
    <mat-card class="permissions-card">
      <mat-card-header>
        <div class="permissions-header">
          <div class="header-title">
            <mat-icon class="section-icon">security</mat-icon>
            <h2>{{ selectedRole.name }} Permissions for {{ selectedModule.name }}</h2>
          </div>
          <div class="bulk-actions">
            <button mat-raised-button color="primary" (click)="assignAllPermissions()" [disabled]="isLoading" class="action-button">
              <mat-icon>add_circle</mat-icon> Assign All
            </button>
            <button mat-raised-button color="warn" (click)="removeAllPermissions()" [disabled]="isLoading" class="action-button">
              <mat-icon>remove_circle</mat-icon> Remove All
            </button>
          </div>
        </div>
      </mat-card-header>

      <mat-divider></mat-divider>

      <mat-card-content>
        <div class="permissions-list">
          <div *ngIf="permissions.length === 0" class="no-permissions">
            <mat-icon class="empty-icon">info</mat-icon>
            <p>No permissions available. Please check the backend configuration.</p>
          </div>

          <div class="permissions-grid">
            <div *ngFor="let permission of permissions" class="permission-item" [class.assigned]="assignedPermissionIds.has(permission.id)">
              <mat-checkbox
                class="permission-checkbox"
                [checked]="assignedPermissionIds.has(permission.id)"
                (change)="togglePermission(permission.id, assignedPermissionIds.has(permission.id))"
                [disabled]="isLoading"
                [matTooltip]="permission.description || ''"
                matTooltipPosition="right"
                color="primary">
              </mat-checkbox>
              <div class="permission-details" (click)="toggleCheckbox(permission.id)">
                <div class="permission-name-container">
                  <span class="permission-name">{{ permission.name | titlecase }}</span>
                  <mat-chip-listbox>
                    <mat-chip [ngClass]="assignedPermissionIds.has(permission.id) ? 'assigned-chip' : 'not-assigned-chip'">
                      {{ assignedPermissionIds.has(permission.id) ? 'Assigned' : 'Not Assigned' }}
                    </mat-chip>
                  </mat-chip-listbox>
                </div>
                <p class="permission-description" *ngIf="permission.description">
                  {{ permission.description }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </ng-container>

  <div class="no-selection" *ngIf="(!selectedRole || !selectedModule) && !isLoading">
    <mat-icon class="large-icon">tune</mat-icon>
    <h3 *ngIf="!selectedRole">Please select a role to manage permissions</h3>
    <h3 *ngIf="selectedRole && !selectedModule">Please select a module to manage permissions for {{ selectedRole.name }}</h3>
    <p>Use the dropdowns above to select a role and module to manage permissions</p>
  </div>
</div>