<!-- src/app/features/role-management/role-detail/role-detail.component.html -->

<div class="role-detail-container">
    <div *ngIf="isLoading" class="loading-spinner">
      <mat-spinner diameter="40"></mat-spinner>
      <p>Loading role details...</p>
    </div>
  
    <div *ngIf="errorMessage" class="error-message">
      <p>{{ errorMessage }}</p>
    </div>
  
    <div *ngIf="!isLoading && !errorMessage && role">
      <div class="header-actions">
        <button mat-button color="primary" (click)="goBack()">
          <mat-icon>arrow_back</mat-icon> Back to Roles
        </button>
        <button mat-raised-button color="primary" (click)="editRole()">
          <mat-icon>edit</mat-icon> Edit Role
        </button>
      </div>
  
      <mat-card>
        <mat-card-header>
          <mat-card-title>{{ role.name }}</mat-card-title>
          <mat-card-subtitle>
            <mat-chip [color]="role.is_active ? 'accent' : 'warn'" selected>
              {{ role.is_active ? 'Active' : 'Inactive' }}
            </mat-chip>
          </mat-card-subtitle>
        </mat-card-header>
  
        <mat-card-content>
          <div class="role-info">
            <h3>Role Details</h3>
            <div class="detail-item">
              <span class="label">ID:</span>
              <span class="value">{{ role.id }}</span>
            </div>
            
            <div class="detail-item">
              <span class="label">Description:</span>
              <span class="value">{{ role.description || 'No description available' }}</span>
            </div>
            
            <div class="detail-item" *ngIf="role.created_at">
              <span class="label">Created At:</span>
              <span class="value">{{ role.created_at | date:'medium' }}</span>
            </div>
            
            <div class="detail-item" *ngIf="role.creator_name">
              <span class="label">Created By:</span>
              <span class="value">{{ role.creator_name }}</span>
            </div>
          </div>
          
          <mat-divider></mat-divider>
          
          <div class="users-with-role">
            <h3>Users with this Role</h3>
            
            <div *ngIf="users.length === 0" class="no-users">
              <p>No users have been assigned this role.</p>
            </div>
            
            <mat-list *ngIf="users.length > 0">
              <mat-list-item *ngFor="let user of users">
                <mat-icon matListItemIcon>person</mat-icon>
                <div matListItemTitle>{{ user.name }}</div>
                <div matListItemLine>{{ user.email }}</div>
              </mat-list-item>
            </mat-list>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>