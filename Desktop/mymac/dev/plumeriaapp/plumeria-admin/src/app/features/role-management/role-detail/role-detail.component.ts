// src/app/features/role-management/role-detail/role-detail.component.ts

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatListModule } from '@angular/material/list';
import { MatDividerModule } from '@angular/material/divider';

import { Role } from '../../../core/models/user';
import { RoleService } from '../../../core/services/role.service';

@Component({
  selector: 'app-role-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatListModule,
    MatDividerModule,
  ],
  templateUrl: './role-detail.component.html',
  styleUrl: './role-detail.component.scss',
})
export class RoleDetailComponent implements OnInit {
  role: Role | null = null;
  users: any[] = [];
  isLoading = true;
  errorMessage = '';

  constructor(
    private roleService: RoleService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.loadRoleData(+params['id']);
      }
    });
  }

  loadRoleData(id: number): void {
    this.isLoading = true;
    this.roleService.getRoleById(id).subscribe({
      next: (response) => {
        this.role = response.data;
        this.loadRoleUsers(id);
      },
      error: (error) => {
        this.errorMessage = 'Error loading role: ' + error.message;
        this.isLoading = false;
      },
    });
  }

  loadRoleUsers(roleId: number): void {
    this.roleService.getUsersWithRole(roleId).subscribe({
      next: (response) => {
        this.users = response.data;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Error loading role users: ' + error.message;
        this.isLoading = false;
      },
    });
  }

  editRole(): void {
    if (this.role) {
      this.router.navigate(['/admin/roles/edit', this.role.id]);
    }
  }

  goBack(): void {
    this.router.navigate(['/admin/roles']);
  }
}
