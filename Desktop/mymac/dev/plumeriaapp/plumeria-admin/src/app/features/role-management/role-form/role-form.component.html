<!-- src/app/features/role-management/role-form/role-form.component.html -->

<div class="role-form-container">
    <mat-card>
      <mat-card-header>
        <mat-card-title>{{ isEditMode ? 'Edit Role' : 'Add New Role' }}</mat-card-title>
      </mat-card-header>
  
      <mat-card-content>
        <div *ngIf="isLoading" class="loading-spinner">
          <mat-spinner diameter="40"></mat-spinner>
        </div>
  
        <div *ngIf="errorMessage" class="error-message">
          {{ errorMessage }}
        </div>
  
        <form [formGroup]="roleForm" (ngSubmit)="onSubmit()" *ngIf="!isLoading">
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Role Name</mat-label>
              <input matInput formControlName="name" placeholder="Enter role name">
              <mat-error *ngIf="roleForm.get('name')?.hasError('required')">
                Role name is required
              </mat-error>
              <mat-error *ngIf="roleForm.get('name')?.hasError('minlength')">
                Role name must be at least 3 characters
              </mat-error>
            </mat-form-field>
          </div>
  
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>Description</mat-label>
              <textarea matInput formControlName="description" placeholder="Enter role description" rows="3"></textarea>
            </mat-form-field>
          </div>
  
          <div class="form-row">
            <mat-slide-toggle formControlName="is_active" color="primary">
              {{ roleForm.get('is_active')?.value ? 'Active' : 'Inactive' }}
            </mat-slide-toggle>
          </div>
        </form>
      </mat-card-content>
  
      <mat-card-actions align="end">
        <button mat-button type="button" (click)="cancel()">Cancel</button>
        <button mat-raised-button color="primary" [disabled]="isLoading || roleForm.invalid" (click)="onSubmit()">
          {{ isEditMode ? 'Update' : 'Create' }}
        </button>
      </mat-card-actions>
    </mat-card>
  </div>