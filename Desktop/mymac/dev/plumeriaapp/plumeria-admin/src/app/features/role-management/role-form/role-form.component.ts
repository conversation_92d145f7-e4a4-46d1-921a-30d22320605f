// src/app/features/role-management/role-form/role-form.component.ts

import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { RoleService } from '../../../core/services/role.service';

@Component({
  selector: 'app-role-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCardModule,
    MatSlideToggleModule,
    MatIconModule,
    MatProgressSpinnerModule,
    RouterModule,
  ],
  templateUrl: './role-form.component.html',
  styleUrl: './role-form.component.scss',
})
export class RoleFormComponent implements OnInit {
  roleForm!: FormGroup;
  isEditMode = false;
  roleId: number | null = null;
  isLoading = false;
  errorMessage = '';

  constructor(
    private fb: FormBuilder,
    private roleService: RoleService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.createForm();

    // Check if we're in edit mode
    this.route.params.subscribe((params) => {
      if (params['id']) {
        this.roleId = +params['id'];
        this.isEditMode = true;
        this.loadRoleData();
      }
    });
  }

  createForm(): void {
    this.roleForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3)]],
      description: [''],
      is_active: [true],
    });
  }

  loadRoleData(): void {
    if (!this.roleId) return;

    this.isLoading = true;
    this.roleService.getRoleById(this.roleId).subscribe({
      next: (response) => {
        const role = response.data;

        this.roleForm.patchValue({
          name: role.name,
          description: role.description || '',
          is_active: role.is_active,
        });

        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Error loading role: ' + error.message;
        this.isLoading = false;
      },
    });
  }

  onSubmit(): void {
    if (this.roleForm.invalid) {
      // Mark all fields as touched to trigger validation visuals
      Object.keys(this.roleForm.controls).forEach((key) => {
        const control = this.roleForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isLoading = true;
    const roleData = this.roleForm.value;

    if (this.isEditMode && this.roleId) {
      // Update existing role
      this.roleService.updateRole(this.roleId, roleData).subscribe({
        next: () => {
          this.isLoading = false;
          this.router.navigate(['/admin/roles']);
        },
        error: (error) => {
          this.errorMessage = 'Error updating role: ' + error.message;
          this.isLoading = false;
        },
      });
    } else {
      // Create new role
      this.roleService.createRole(roleData).subscribe({
        next: () => {
          this.isLoading = false;
          this.router.navigate(['/admin/roles']);
        },
        error: (error) => {
          this.errorMessage = 'Error creating role: ' + error.message;
          this.isLoading = false;
        },
      });
    }
  }

  cancel(): void {
    this.router.navigate(['/admin/roles']);
  }
}
