.staff-form-container {
  padding: 24px;
  max-width: 900px;
  margin: 0 auto;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;

  h1 {
    font-size: 24px;
    font-weight: 500;
    color: #333;
    margin: 0;
  }
}

.header-actions {
  display: flex;
  gap: 12px;

  button {
    min-width: 120px;
    height: 40px;
    border-radius: 6px;
    font-weight: 500;
    text-transform: none;

    mat-icon {
      margin-right: 8px;
      font-size: 18px;
    }

    &[color="accent"] {
      background-color: #ff4081;
      color: white;
      border: 1px solid #ff4081;

      &:hover {
        background-color: #e91e63;
        border-color: #e91e63;
      }

      &:disabled {
        background-color: #f5f5f5;
        color: #bdbdbd;
        border-color: #e0e0e0;
      }
    }
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;

  mat-spinner {
    margin-bottom: 16px;
  }

  p {
    color: #666;
    font-size: 16px;
    margin: 0;
  }
}

.form-card {
  width: 100%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

mat-card-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;

  mat-card-title {
    font-size: 24px;
    font-weight: 500;
    color: #333;
  }
}

.loading-shade {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.15);
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  text-align: center;
}

.error-message {
  color: #f44336;
  margin-bottom: 16px;
  font-size: 16px;
}

// Form layout with 3 fields per row
.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  align-items: flex-start;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-field {
  flex: 1;
  min-width: 0; // Prevents flex items from overflowing

  &.full-width {
    flex: 1 1 100%;
  }

  &.half-width {
    flex: 1 1 calc(50% - 10px);
  }

  &.third-width {
    flex: 1 1 calc(33.333% - 14px);
  }
}

mat-form-field {
  width: 100%;

  .mat-mdc-form-field-label {
    font-weight: 500;
    color: #555;
  }

  &.mat-focused .mat-mdc-form-field-label {
    color: #3f51b5;
  }
}

// Special styling for specific field types
.photo-upload-field {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
  text-align: center;

  &:hover {
    border-color: #3f51b5;
  }
}

.toggle-field {
  margin: 16px 0;

  mat-slide-toggle {
    .mat-mdc-slide-toggle-label {
      font-weight: 500;
    }
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;

  button {
    min-width: 120px;
    height: 44px;
    border-radius: 6px;
    font-weight: 500;
    text-transform: none;

    &[mat-raised-button] {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      &:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }
    }

    mat-icon {
      margin-right: 8px;
      font-size: 18px;
    }
  }
}

// Tab styling for organized sections
mat-tab-group {
  margin-bottom: 24px;

  .mat-mdc-tab-header {
    border-bottom: 1px solid #e0e0e0;
  }

  .mat-mdc-tab-label {
    min-width: 120px;
    padding: 0 16px;
    font-weight: 500;

    &.mat-mdc-tab-label-active {
      color: #3f51b5;
    }
  }
}

.tab-content {
  padding: 24px 0;

  h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 500;
    color: #333;
    border-bottom: 2px solid #3f51b5;
    padding-bottom: 8px;
    display: inline-block;
  }
}

// Responsive design
@media (max-width: 1024px) {
  .staff-form-container {
    padding: 16px;
  }

  .form-row {
    gap: 16px;
  }

  .form-field {
    &.third-width {
      flex: 1 1 calc(50% - 8px);
    }
  }
}

@media (max-width: 768px) {
  .staff-form-container {
    padding: 12px;
  }

  .form-row {
    flex-direction: column;
    gap: 12px;
  }

  .form-field {
    &.third-width,
    &.half-width {
      flex: 1 1 100%;
    }
  }

  .form-actions {
    flex-direction: column-reverse;

    button {
      width: 100%;
    }
  }

  mat-tab-group {
    .mat-mdc-tab-label {
      min-width: 80px;
      padding: 0 8px;
      font-size: 14px;
    }
  }
}

@media (max-width: 480px) {
  mat-card-header {
    mat-card-title {
      font-size: 20px;
    }
  }

  .tab-content h3 {
    font-size: 16px;
  }
}

// Professional Searchable Dropdown Styling
.search-option {
  padding: 0 !important;
  height: auto !important;
  min-height: 64px !important;
  pointer-events: none !important;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
  border-bottom: 2px solid #e3f2fd !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;

  .search-container {
    width: 100%;
    padding: 16px 20px;
    pointer-events: all;
    position: relative;

    .search-input {
      width: 100%;
      padding: 14px 20px 14px 48px;
      border: 2px solid #e1e5e9;
      border-radius: 12px;
      font-size: 14px;
      font-weight: 500;
      background-color: #ffffff;
      outline: none;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      color: #2c3e50;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

      &:focus {
        border-color: #3f51b5;
        box-shadow: 0 0 0 3px rgba(63, 81, 181, 0.12), 0 4px 12px rgba(0, 0, 0, 0.15);
        transform: translateY(-1px);
      }

      &::placeholder {
        color: #7c8db5;
        font-weight: 400;
        font-style: normal;
      }
    }

    // Search icon
    &::before {
      content: '🔍';
      position: absolute;
      left: 32px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 16px;
      z-index: 1;
      pointer-events: none;
    }
  }
}

.divider-option {
  padding: 0 !important;
  height: 2px !important;
  min-height: 2px !important;

  .dropdown-divider {
    margin: 0;
    border: none;
    border-top: 2px solid #e3f2fd;
    width: 100%;
    background: linear-gradient(90deg, #e3f2fd, #f5f5f5, #e3f2fd);
  }
}

.department-option,
.designation-option,
.qualification-option,
.employment-type-option {
  padding: 12px 20px !important;
  min-height: 48px !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  border-left: 3px solid transparent !important;

  .option-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .department-name,
    .designation-name,
    .qualification-name,
    .employment-type-name {
      font-weight: 500;
      color: #2c3e50;
      font-size: 14px;
      line-height: 1.4;
    }

    .exact-match-icon {
      color: #4caf50;
      font-size: 18px;
      opacity: 0.8;
    }
  }

  &:hover {
    background-color: rgba(63, 81, 181, 0.06) !important;
    border-left-color: #3f51b5 !important;

    .option-content {
      .department-name,
      .designation-name,
      .qualification-name,
      .employment-type-name {
        color: #3f51b5;
        font-weight: 600;
      }
    }
  }

  &.exact-match {
    background-color: rgba(76, 175, 80, 0.05) !important;
    border-left-color: #4caf50 !important;
  }
}

.create-new-option {
  background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%) !important;
  border: 2px solid #e3f2fd !important;
  border-radius: 8px !important;
  color: #1976d2 !important;
  font-weight: 500 !important;
  margin: 8px 16px !important;
  padding: 12px 20px !important;
  min-height: 52px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 2px 4px rgba(25, 118, 210, 0.1) !important;

  .create-option-content {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 0;

    mat-icon {
      color: #4caf50;
      font-size: 22px;
      width: 22px;
      height: 22px;
      background: rgba(76, 175, 80, 0.1);
      border-radius: 50%;
      padding: 4px;
      transition: all 0.3s ease;
    }

    span {
      color: #1976d2;
      font-weight: 600;
      font-size: 14px;
      flex: 1;
    }
  }

  &:hover {
    background: linear-gradient(135deg, #c8e6c9 0%, #e3f2fd 100%) !important;
    border-color: #1976d2 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2) !important;

    .create-option-content {
      mat-icon {
        color: #388e3c;
        background: rgba(56, 142, 60, 0.15);
        transform: scale(1.1);
      }

      span {
        color: #1565c0;
      }
    }
  }
}

.no-results-option, .empty-state-option {
  padding: 16px 20px !important;
  min-height: 56px !important;
  background-color: #fafafa !important;

  .no-results-content, .empty-state-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    color: #7c8db5;
    font-style: normal;
    font-weight: 500;
    text-align: center;

    mat-icon {
      color: #bdbdbd;
      font-size: 20px;
      opacity: 0.7;
    }
  }
}

.loading-option {
  padding: 16px 20px !important;
  min-height: 56px !important;
  background-color: #f8f9fa !important;

  .loading-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    color: #3f51b5;
    font-weight: 500;

    mat-spinner {
      opacity: 0.8;
    }
  }
}

// Mobile App Access Section Styling
.mobile-access-section {
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 24px;
  margin: 16px 0;

  h3 {
    color: #333;
    margin-bottom: 16px;
    border-bottom: 2px solid #3f51b5;
    padding-bottom: 8px;
    display: inline-block;
  }

  h4 {
    color: #333;
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 500;
  }

  .mobile-status {
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 16px;
  }
}

// Override Material Select Panel Styling
.mat-mdc-select-panel {
  max-height: 420px !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 2px 8px rgba(0, 0, 0, 0.08) !important;
  border: 1px solid #e3f2fd !important;
  overflow: hidden !important;
  background: #ffffff !important;

  .mat-mdc-option {
    font-family: 'Roboto', sans-serif !important;

    &:hover:not(.mat-mdc-option-disabled) {
      background-color: rgba(63, 81, 181, 0.06) !important;
    }

    &.mat-mdc-option-active {
      background-color: rgba(63, 81, 181, 0.1) !important;
      color: #3f51b5 !important;
      font-weight: 600 !important;
    }

    &.mat-mdc-option-selected {
      background-color: rgba(63, 81, 181, 0.08) !important;
      color: #3f51b5 !important;
      font-weight: 600 !important;
    }
  }
}

// Custom scrollbar for dropdown
.mat-mdc-select-panel::-webkit-scrollbar {
  width: 6px;
}

.mat-mdc-select-panel::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.mat-mdc-select-panel::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;

  &:hover {
    background: #a8a8a8;
  }
}