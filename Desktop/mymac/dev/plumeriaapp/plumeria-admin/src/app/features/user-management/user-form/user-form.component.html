<!-- src/app/features/user-management/user-form/user-form.component.html -->

<div class="user-form-container">
  <mat-card>
    <mat-card-header>
      <mat-card-title>{{ isEditMode ? 'Edit User' : 'Add New User' }}</mat-card-title>
    </mat-card-header>

    <mat-card-content>
      <div *ngIf="isLoading" class="loading-spinner">
        <mat-spinner diameter="40"></mat-spinner>
      </div>

      <div *ngIf="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <form [formGroup]="userForm" (ngSubmit)="onSubmit()" *ngIf="!isLoading">
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Username</mat-label>
            <input matInput formControlName="username" placeholder="Enter username">
            <mat-error *ngIf="userForm.get('username')?.hasError('required')">
              Username is required
            </mat-error>
            <mat-error *ngIf="userForm.get('username')?.hasError('minlength')">
              Username must be at least 4 characters
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Email</mat-label>
            <input matInput formControlName="email" placeholder="Enter email" type="email">
            <mat-error *ngIf="userForm.get('email')?.hasError('required')">
              Email is required
            </mat-error>
            <mat-error *ngIf="userForm.get('email')?.hasError('email')">
              Please enter a valid email address
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>{{ isEditMode ? 'New Password' : 'Password' }}</mat-label>
            <input matInput formControlName="password" [type]="showPassword ? 'text' : 'password'" placeholder="Enter password">
            <button mat-icon-button matSuffix type="button" (click)="togglePasswordVisibility()">
              <mat-icon>{{ showPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
            </button>
            <mat-error *ngIf="userForm.get('password')?.hasError('required')">
              Password is required
            </mat-error>
            <mat-error *ngIf="userForm.get('password')?.hasError('minlength')">
              Password must be at least 6 characters
            </mat-error>
          </mat-form-field>

          <div *ngIf="isEditMode && userForm.get('password')?.disabled">
            <button type="button" mat-button color="primary" (click)="enablePasswordField()">
              Change Password
            </button>
          </div>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Full Name</mat-label>
            <input matInput formControlName="name" placeholder="Enter full name">
            <mat-error *ngIf="userForm.get('name')?.hasError('required')">
              Name is required
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Phone Number</mat-label>
            <input matInput formControlName="phone" placeholder="Enter phone number">
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>Roles</mat-label>
            <!-- Multiple select role dropdown -->
            <mat-select formControlName="roles" multiple>
              <mat-option *ngFor="let role of roles" [value]="role.name">
                {{ role.name }}
              </mat-option>
            </mat-select>
            <mat-hint>Select one or more roles</mat-hint>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-slide-toggle formControlName="is_active" color="primary">
            {{ userForm.get('is_active')?.value ? 'Active' : 'Inactive' }}
          </mat-slide-toggle>
        </div>
      </form>
    </mat-card-content>

    <mat-card-actions align="end">
      <button mat-button type="button" (click)="cancel()">Cancel</button>
      <button mat-raised-button color="primary" [disabled]="isLoading || userForm.invalid" (click)="onSubmit()">
        {{ isEditMode ? 'Update' : 'Create' }}
      </button>
    </mat-card-actions>
  </mat-card>
</div>