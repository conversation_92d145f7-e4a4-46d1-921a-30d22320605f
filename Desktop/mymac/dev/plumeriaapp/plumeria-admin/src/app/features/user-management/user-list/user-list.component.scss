// src/app/features/user-management/user-list/user-list.component.scss

.user-list-container {
    padding: 24px;
  
    .user-list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
  
      h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 500;
      }
    }
  
    .filter-container {
      margin-bottom: 24px;
  
      mat-form-field {
        width: 100%;
        max-width: 500px;
      }
    }
  
    .table-container {
      background-color: white;
      border-radius: 4px;
      overflow: hidden;
    }
  
    .user-table {
      width: 100%;
  
      .mat-column-id {
        width: 70px;
        padding-right: 24px;
      }
  
      .mat-column-actions {
        width: 80px;
        text-align: right;
      }
  
      .mat-column-status {
        width: 120px;
      }
    }
  
    .spinner-container, .error-container, .no-data-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px;
      text-align: center;
    }
  
    .error-container {
      color: #f44336;
    }
  
    .no-data {
      color: rgba(0, 0, 0, 0.54);
      font-style: italic;
    }
  }