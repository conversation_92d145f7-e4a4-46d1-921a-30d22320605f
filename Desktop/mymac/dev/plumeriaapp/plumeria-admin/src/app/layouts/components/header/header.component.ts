import { Component, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterLink } from '@angular/router';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatBadgeModule } from '@angular/material/badge';
import { AuthService } from '../../../core/services/auth.service';
import { MatDividerModule } from '@angular/material/divider';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    MatToolbarModule,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
    MatBadgeModule,
    MatDividerModule,
  ],
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
})
export class HeaderComponent {
  @Output() toggleSidenav = new EventEmitter<void>();

  notifications = [
    {
      type: 'system',
      icon: 'info',
      text: 'System maintenance scheduled',
      time: '20 minutes ago',
    },
    {
      type: 'user',
      icon: 'person',
      text: 'New user registered',
      time: '1 hour ago',
    },
    {
      type: 'alert',
      icon: 'warning',
      text: 'Login attempt from unknown device',
      time: '2 hours ago',
    },
    {
      type: 'system',
      icon: 'update',
      text: 'System updated to version 2.1.0',
      time: '1 day ago',
    },
  ];

  constructor(public authService: AuthService, private router: Router) {}

  onToggleSidenav(): void {
    this.toggleSidenav.emit();
  }

  navigateToProfile(): void {
    this.router.navigate(['/admin/profile']);
  }

  navigateToSettings(): void {
    this.router.navigate(['/admin/settings']);
  }

  markAllAsRead(): void {
    // Implement mark all as read functionality
    console.log('Marked all notifications as read');
  }

  logout(): void {
    this.authService.logout();
  }

  getInitials(): string {
    const user = this.authService.currentUserValue;
    if (user && user.name) {
      return user.name.charAt(0).toUpperCase();
    } else if (user && user.username) {
      return user.username.charAt(0).toUpperCase();
    }
    return 'A'; // Default fallback
  }

  getNotificationCount(): number {
    return this.notifications.length;
  }
}
