// Color variables
$primary-color: #3498db;
$accent-color: #f39c12;
$warn-color: #e74c3c;
$success-color: #2ecc71;
$info-color: #3498db;

$text-color: #333333;
$text-light-color: #666666;
$text-lighter-color: #999999;

$background-color: #f5f7fa;
$card-background: #ffffff;
$border-color: #e0e0e0;

$sidebar-bg: #2c3e50;
$sidebar-text: #ecf0f1;
$sidebar-active: #3498db;
$sidebar-hover: rgba(255, 255, 255, 0.1);

// Status colors
$status-active-color: #2ecc71;
$status-inactive-color: #e74c3c;

// Message colors
$error-bg: #ffebee;
$error-color: #c62828;
$success-bg: #e8f5e9;
$success-color: #2e7d32;
$info-bg: #e3f2fd;
$info-color: #1565c0;

// Spacing variables
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;

// Card variables
$card-padding: $spacing-md;
$card-header-padding: $spacing-md;
$card-border-radius: 4px;
$card-margin-bottom: $spacing-lg;

// Form variables
$form-field-margin: $spacing-md 0;
$form-field-width: 100%;

// Table variables
$table-min-width: 800px;
$table-header-font-weight: 500;
$table-header-color: rgba(0, 0, 0, 0.87);

// Responsive breakpoints
$breakpoint-xs: 576px;
$breakpoint-sm: 768px;
$breakpoint-md: 992px;
$breakpoint-lg: 1200px;

// Animation variables
$transition-speed: 0.3s;
$transition-function: cubic-bezier(0.4, 0, 0.2, 1);
