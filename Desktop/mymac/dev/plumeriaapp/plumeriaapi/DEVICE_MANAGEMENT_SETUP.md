# Mobile Device Management Setup Guide

## Overview
This guide helps you set up the device management system for your mobile APIs. The system tracks device information, sessions, and security logs for staff mobile app access.

## 📋 Prerequisites
- Existing staff records in the database (you have IDs: 13, 16, 17)
- MySQL database running
- Node.js API server

## 🗄️ Database Setup

### Step 1: Create Device Tables
Run the main device tables creation script:
```bash
mysql -u your_username -p your_database < create_device_tables.sql
```

### Step 2: Insert Test Data
Run the test data script with your existing staff IDs:
```bash
mysql -u your_username -p your_database < insert_device_test_data.sql
```

### Step 3: Verify Tables Created
Check that the tables were created successfully:
```sql
SHOW TABLES LIKE 'staff_device%';
SELECT COUNT(*) FROM staff_devices;
SELECT COUNT(*) FROM staff_device_sessions;
SELECT COUNT(*) FROM staff_device_logs;
```

## 📱 Mobile API Endpoints

### Base URL: `http://localhost:3000/api/mobile/v1`

### Required Headers for All Requests:
```
Content-Type: application/json
x-platform: android|ios
x-app-version: 1.0.0
x-device-id: unique_device_identifier
```

### Authentication Endpoints:

#### 1. **POST** `/auth/login`
Login with mobile and password
```json
{
  "staff_mobile": "**********",
  "password": "password123",
  "device_info": {
    "device_id": "unique_device_id",
    "device_name": "User's Phone",
    "platform": "android",
    "device_model": "Samsung Galaxy S23",
    "os_version": "Android 13",
    "app_version": "1.0.0"
  }
}
```

#### 2. **POST** `/auth/otp/send`
Send OTP for verification
```json
{
  "staff_mobile": "**********"
}
```

#### 3. **POST** `/auth/otp/verify`
Verify OTP and login
```json
{
  "staff_mobile": "**********",
  "otp": "123456",
  "device_info": { ... }
}
```

#### 4. **GET** `/auth/profile` (Protected)
Get staff profile information
```
Authorization: Bearer <access_token>
```

#### 5. **POST** `/auth/token/refresh`
Refresh access token
```json
{
  "refresh_token": "your_refresh_token"
}
```

## 🧪 Testing the APIs

### Method 1: Using the Test Script
```bash
cd /path/to/your/api
npm install axios  # if not already installed
node test_mobile_api.js
```

### Method 2: Using cURL
```bash
# Test health check
curl -X GET http://localhost:3000/api/mobile/v1/health

# Test login
curl -X POST http://localhost:3000/api/mobile/v1/auth/login \
  -H "Content-Type: application/json" \
  -H "x-platform: android" \
  -H "x-app-version: 1.0.0" \
  -H "x-device-id: test_device_123" \
  -d '{
    "staff_mobile": "**********",
    "password": "password123",
    "device_info": {
      "device_id": "test_device_123",
      "platform": "android",
      "app_version": "1.0.0"
    }
  }'
```

### Method 3: Using Postman
1. Import the collection with the endpoints above
2. Set environment variables for base URL and tokens
3. Test each endpoint sequentially

## 🔧 Configuration

### Environment Variables
Make sure these are set in your `.env` file:
```
JWT_SECRET=your_jwt_secret
JWT_REFRESH_SECRET=your_jwt_refresh_secret
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
```

### Staff Password Setup
For testing, you need to set passwords for your staff members:
```sql
-- Update staff passwords (use bcrypt hashed passwords in production)
UPDATE staffs SET password = '$2b$12$hashed_password_here' WHERE id IN (13, 16, 17);
```

## 📊 Device Management Features

### Device Tracking
- Unique device identification
- Platform detection (Android/iOS)
- Device model and OS version tracking
- App version monitoring
- Push token management

### Session Management
- JWT token tracking
- Session expiry management
- Multiple device support
- Automatic cleanup of expired sessions

### Security Logging
- Login success/failure tracking
- Device registration events
- Suspicious activity detection
- Risk scoring system
- IP address monitoring

### Database Tables Created:

1. **`staff_devices`** - Device information
2. **`staff_device_sessions`** - Active sessions
3. **`staff_device_logs`** - Security and activity logs

## 🔍 Monitoring and Maintenance

### Check Device Activity
```sql
-- View recent device activity
SELECT sd.device_name, sd.platform, sd.last_seen, s.staff_name
FROM staff_devices sd
JOIN staffs s ON sd.staff_id = s.id
ORDER BY sd.last_seen DESC;
```

### Check Security Logs
```sql
-- View recent security events
SELECT sdl.event_type, sdl.event_description, sdl.ip_address, s.staff_name, sdl.created_at
FROM staff_device_logs sdl
LEFT JOIN staffs s ON sdl.staff_id = s.id
ORDER BY sdl.created_at DESC
LIMIT 20;
```

### Cleanup Old Data
```sql
-- Clean up old sessions (older than 30 days)
DELETE FROM staff_device_sessions 
WHERE logout_time < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- Clean up old logs (older than 90 days, low risk only)
DELETE FROM staff_device_logs 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY) 
AND risk_score < 5;
```

## 🚨 Troubleshooting

### Common Issues:

1. **Foreign Key Constraint Error**
   - Ensure staff IDs exist in the staffs table
   - Check the insert_device_test_data.sql script

2. **Authentication Failures**
   - Verify staff passwords are set correctly
   - Check JWT secrets in environment variables

3. **Device Registration Issues**
   - Ensure device_id is unique per staff member
   - Check required headers are being sent

4. **Session Management Problems**
   - Verify token expiry settings
   - Check database session records

## 📞 Support
If you encounter issues:
1. Check the API server logs
2. Verify database connections
3. Test with the provided test script
4. Review the mobile API response format
