-- Device Management Tables for Mobile App
-- Created: 2024

-- Table to store device information
DROP TABLE IF EXISTS `staff_devices`;
CREATE TABLE `staff_devices` (
  `id` int NOT NULL AUTO_INCREMENT,
  `staff_id` int NOT NULL,
  `device_id` varchar(255) NOT NULL COMMENT 'Unique device identifier from mobile app',
  `device_name` varchar(100) DEFAULT NULL COMMENT 'User-friendly device name',
  `platform` enum('android','ios','web') NOT NULL DEFAULT 'android',
  `device_model` varchar(100) DEFAULT NULL COMMENT 'Device model (e.g., iPhone 14, Samsung Galaxy S23)',
  `os_version` varchar(50) DEFAULT NULL COMMENT 'Operating system version',
  `app_version` varchar(20) DEFAULT NULL COMMENT 'Mobile app version',
  `push_token` varchar(500) DEFAULT NULL COMMENT 'FCM/APNS push notification token',
  `user_agent` text DEFAULT NULL COMMENT 'Browser user agent string',
  `is_active` boolean DEFAULT TRUE COMMENT 'Whether device is currently active',
  `is_trusted` boolean DEFAULT FALSE COMMENT 'Whether device is marked as trusted',
  `last_login` timestamp NULL DEFAULT NULL COMMENT 'Last login from this device',
  `last_seen` timestamp NULL DEFAULT NULL COMMENT 'Last activity from this device',
  `login_count` int DEFAULT 0 COMMENT 'Number of times logged in from this device',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'Last known IP address',
  `location_info` json DEFAULT NULL COMMENT 'Location information if available',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL,
  `updated_by` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_staff_device` (`staff_id`, `device_id`),
  KEY `idx_staff_devices_staff_id` (`staff_id`),
  KEY `idx_staff_devices_device_id` (`device_id`),
  KEY `idx_staff_devices_platform` (`platform`),
  KEY `idx_staff_devices_active` (`is_active`),
  KEY `idx_staff_devices_last_seen` (`last_seen`),
  CONSTRAINT `fk_staff_devices_staff` FOREIGN KEY (`staff_id`) REFERENCES `staffs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Device information for staff mobile app access';

-- Table to store device login sessions
DROP TABLE IF EXISTS `staff_device_sessions`;
CREATE TABLE `staff_device_sessions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `staff_id` int NOT NULL,
  `device_id` int NOT NULL COMMENT 'Reference to staff_devices.id',
  `session_token` varchar(255) NOT NULL COMMENT 'JWT token identifier or session ID',
  `refresh_token_hash` varchar(255) DEFAULT NULL COMMENT 'Hashed refresh token',
  `login_time` timestamp DEFAULT CURRENT_TIMESTAMP,
  `logout_time` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `is_active` boolean DEFAULT TRUE,
  `logout_reason` enum('manual','expired','forced','device_removed') DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_device_sessions_staff_id` (`staff_id`),
  KEY `idx_device_sessions_device_id` (`device_id`),
  KEY `idx_device_sessions_token` (`session_token`),
  KEY `idx_device_sessions_active` (`is_active`),
  KEY `idx_device_sessions_expires` (`expires_at`),
  CONSTRAINT `fk_device_sessions_staff` FOREIGN KEY (`staff_id`) REFERENCES `staffs` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_device_sessions_device` FOREIGN KEY (`device_id`) REFERENCES `staff_devices` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Active sessions for staff devices';

-- Table to log device activities and security events
DROP TABLE IF EXISTS `staff_device_logs`;
CREATE TABLE `staff_device_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `staff_id` int NOT NULL,
  `device_id` int DEFAULT NULL COMMENT 'Reference to staff_devices.id, NULL for failed attempts',
  `device_identifier` varchar(255) DEFAULT NULL COMMENT 'Device ID from request headers',
  `event_type` enum('login_success','login_failed','logout','token_refresh','device_registered','device_removed','suspicious_activity') NOT NULL,
  `event_description` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `platform` varchar(20) DEFAULT NULL,
  `app_version` varchar(20) DEFAULT NULL,
  `location_info` json DEFAULT NULL,
  `additional_data` json DEFAULT NULL COMMENT 'Additional event-specific data',
  `risk_score` tinyint DEFAULT 0 COMMENT 'Security risk score 0-10',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_device_logs_staff_id` (`staff_id`),
  KEY `idx_device_logs_device_id` (`device_id`),
  KEY `idx_device_logs_event_type` (`event_type`),
  KEY `idx_device_logs_created_at` (`created_at`),
  KEY `idx_device_logs_risk_score` (`risk_score`),
  CONSTRAINT `fk_device_logs_staff` FOREIGN KEY (`staff_id`) REFERENCES `staffs` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_device_logs_device` FOREIGN KEY (`device_id`) REFERENCES `staff_devices` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Device activity and security logs';

-- Insert sample device data for testing using existing staff IDs (13, 16, 17)
INSERT INTO `staff_devices` (
  `staff_id`, `device_id`, `device_name`, `platform`, `device_model`,
  `os_version`, `app_version`, `is_active`, `is_trusted`, `created_by`
) VALUES
-- Using actual staff IDs that exist in your database
(13, 'device_android_001', 'Staff 13 Samsung Galaxy', 'android', 'Samsung Galaxy S23', 'Android 13', '1.0.0', TRUE, TRUE, 13),
(13, 'device_ios_001', 'Staff 13 iPhone', 'ios', 'iPhone 14 Pro', 'iOS 16.5', '1.0.0', TRUE, FALSE, 13),
(16, 'device_android_002', 'Staff 16 OnePlus', 'android', 'OnePlus 11', 'Android 13', '1.0.0', TRUE, TRUE, 16),
(17, 'device_ios_002', 'Staff 17 iPad', 'ios', 'iPad Pro 12.9', 'iPadOS 16.5', '1.0.0', TRUE, FALSE, 17);

-- Insert sample session data (device_id references the auto-generated IDs from staff_devices table)
INSERT INTO `staff_device_sessions` (
  `staff_id`, `device_id`, `session_token`, `login_time`, `expires_at`,
  `ip_address`, `is_active`
) VALUES
(13, 1, 'session_token_001', NOW(), DATE_ADD(NOW(), INTERVAL 24 HOUR), '*************', TRUE),
(16, 3, 'session_token_002', NOW(), DATE_ADD(NOW(), INTERVAL 24 HOUR), '*************', TRUE);

-- Insert sample activity logs
INSERT INTO `staff_device_logs` (
  `staff_id`, `device_id`, `device_identifier`, `event_type`, `event_description`,
  `ip_address`, `platform`, `app_version`
) VALUES
(13, 1, 'device_android_001', 'login_success', 'Staff logged in successfully', '*************', 'android', '1.0.0'),
(13, 1, 'device_android_001', 'device_registered', 'New device registered for staff', '*************', 'android', '1.0.0'),
(16, 3, 'device_android_002', 'login_success', 'Staff logged in successfully', '*************', 'android', '1.0.0'),
(17, NULL, 'unknown_device_123', 'login_failed', 'Failed login attempt - invalid credentials', '*************', 'android', '1.0.0');
