require('dotenv').config();
const db = require('./src/config/database');

async function testConnection() {
  try {
    // Test the connection with a simple query
    const result = await db.query('SELECT 1 + 1 AS solution');
    console.log('Database connection successful!');
    console.log('Test query result:', result[0].solution);
    
    // Display some connection pool information
    console.log('Connection pool established with limit:', 25);
    
    // Close the connection pool when done testing
    await db.close();
    console.log('Connection pool closed successfully');
  } catch (error) {
    console.error('Database connection failed:', error);
  }
}

// Run the test
testConnection();