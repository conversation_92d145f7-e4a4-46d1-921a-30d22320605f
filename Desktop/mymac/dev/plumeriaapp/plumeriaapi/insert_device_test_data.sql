-- Insert Device Test Data with Proper Foreign Key References
-- Using existing staff IDs: 13, 16, 17

-- First, verify the staff records exist
SELECT id, staff_name, staff_mobile FROM staffs WHERE id IN (13, 16, 17);

-- Insert sample device data for testing
INSERT INTO `staff_devices` (
  `staff_id`, `device_id`, `device_name`, `platform`, `device_model`, 
  `os_version`, `app_version`, `is_active`, `is_trusted`, `created_by`
) VALUES 
(13, 'device_android_13_001', 'Staff 13 Samsung Galaxy', 'android', 'Samsung Galaxy S23', 'Android 13', '1.0.0', TRUE, TRUE, 13),
(13, 'device_ios_13_001', 'Staff 13 iPhone', 'ios', 'iPhone 14 Pro', 'iOS 16.5', '1.0.0', TRUE, FALSE, 13),
(16, 'device_android_16_001', 'Staff 16 OnePlus', 'android', 'OnePlus 11', 'Android 13', '1.0.0', TRUE, TRUE, 16),
(16, 'device_ios_16_001', 'Staff 16 iPhone', 'ios', 'iPhone 13', 'iOS 16.0', '1.0.0', TRUE, TRUE, 16),
(17, 'device_ios_17_001', 'Staff 17 iPad', 'ios', 'iPad Pro 12.9', 'iPadOS 16.5', '1.0.0', TRUE, FALSE, 17),
(17, 'device_android_17_001', 'Staff 17 Samsung', 'android', 'Samsung Galaxy A54', 'Android 13', '1.0.0', TRUE, TRUE, 17);

-- Get the device IDs that were just created for session data
-- We'll use variables to get the correct device IDs
SET @device_13_android = (SELECT id FROM staff_devices WHERE device_id = 'device_android_13_001' LIMIT 1);
SET @device_13_ios = (SELECT id FROM staff_devices WHERE device_id = 'device_ios_13_001' LIMIT 1);
SET @device_16_android = (SELECT id FROM staff_devices WHERE device_id = 'device_android_16_001' LIMIT 1);
SET @device_16_ios = (SELECT id FROM staff_devices WHERE device_id = 'device_ios_16_001' LIMIT 1);
SET @device_17_ios = (SELECT id FROM staff_devices WHERE device_id = 'device_ios_17_001' LIMIT 1);
SET @device_17_android = (SELECT id FROM staff_devices WHERE device_id = 'device_android_17_001' LIMIT 1);

-- Insert sample session data with proper device references
INSERT INTO `staff_device_sessions` (
  `staff_id`, `device_id`, `session_token`, `login_time`, `expires_at`, 
  `ip_address`, `is_active`
) VALUES 
(13, @device_13_android, 'session_token_13_001', NOW(), DATE_ADD(NOW(), INTERVAL 24 HOUR), '*************', TRUE),
(16, @device_16_android, 'session_token_16_001', NOW(), DATE_ADD(NOW(), INTERVAL 24 HOUR), '*************', TRUE),
(17, @device_17_ios, 'session_token_17_001', NOW(), DATE_ADD(NOW(), INTERVAL 24 HOUR), '*************', TRUE);

-- Insert sample activity logs with proper references
INSERT INTO `staff_device_logs` (
  `staff_id`, `device_id`, `device_identifier`, `event_type`, `event_description`, 
  `ip_address`, `platform`, `app_version`
) VALUES 
(13, @device_13_android, 'device_android_13_001', 'login_success', 'Staff logged in successfully', '*************', 'android', '1.0.0'),
(13, @device_13_android, 'device_android_13_001', 'device_registered', 'New device registered for staff', '*************', 'android', '1.0.0'),
(13, @device_13_ios, 'device_ios_13_001', 'login_success', 'Staff logged in from iPhone', '*************', 'ios', '1.0.0'),
(16, @device_16_android, 'device_android_16_001', 'login_success', 'Staff logged in successfully', '*************', 'android', '1.0.0'),
(16, @device_16_android, 'device_android_16_001', 'device_registered', 'New device registered for staff', '*************', 'android', '1.0.0'),
(17, @device_17_ios, 'device_ios_17_001', 'login_success', 'Staff logged in from iPad', '*************', 'ios', '1.0.0'),
(17, NULL, 'unknown_device_123', 'login_failed', 'Failed login attempt - invalid credentials', '*************', 'android', '1.0.0');

-- Verify the data was inserted correctly
SELECT 'Device Records Created:' as info;
SELECT sd.id, sd.staff_id, sd.device_id, sd.device_name, sd.platform, s.staff_name 
FROM staff_devices sd 
JOIN staffs s ON sd.staff_id = s.id
WHERE sd.staff_id IN (13, 16, 17)
ORDER BY sd.staff_id, sd.id;

SELECT 'Session Records Created:' as info;
SELECT sds.id, sds.staff_id, sds.device_id, s.staff_name, sd.device_name, sds.session_token
FROM staff_device_sessions sds
JOIN staffs s ON sds.staff_id = s.id
JOIN staff_devices sd ON sds.device_id = sd.id
WHERE sds.staff_id IN (13, 16, 17)
ORDER BY sds.staff_id;

SELECT 'Log Records Created:' as info;
SELECT sdl.id, sdl.staff_id, sdl.event_type, sdl.device_identifier, s.staff_name
FROM staff_device_logs sdl
LEFT JOIN staffs s ON sdl.staff_id = s.id
WHERE sdl.staff_id IN (13, 16, 17) OR sdl.staff_id IS NULL
ORDER BY sdl.created_at DESC
LIMIT 10;
