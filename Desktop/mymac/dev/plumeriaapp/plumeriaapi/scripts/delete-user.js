require('dotenv').config();
const db = require('../src/config/database');

async function deleteUser(username) {
  try {
    // First check if the user exists
    const checkQuery = 'SELECT id FROM users WHERE username = ?';
    const users = await db.query(checkQuery, [username]);
    
    if (users.length === 0) {
      console.log(`User '${username}' not found in the database.`);
      process.exit(0);
    }
    
    const userId = users[0].id;
    console.log(`Found user '${username}' with ID ${userId}`);
    
    // Delete user's roles first (foreign key constraint)
    console.log('Deleting user roles...');
    const deleteRolesQuery = 'DELETE FROM user_roles WHERE user_id = ?';
    await db.query(deleteRolesQuery, [userId]);
    
    // Delete the user
    console.log('Deleting user...');
    const deleteUserQuery = 'DELETE FROM users WHERE id = ?';
    await db.query(deleteUserQuery, [userId]);
    
    console.log(`User '${username}' has been successfully deleted.`);
  } catch (error) {
    console.error('Error deleting user:', error);
  } finally {
    // Close the database connection
    process.exit(0);
  }
}

// Get the username from command line arguments
const username = process.argv[2];

if (!username) {
  console.error('Please provide a username to delete.');
  console.error('Usage: node delete-user.js <username>');
  process.exit(1);
}

console.log(`Attempting to delete user '${username}'...`);
deleteUser(username);
