const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const Staff = require('./src/models/staff.model');

async function simpleLoginTest() {
  try {
    console.log('🔍 Testing simple login for mobile: **********');
    
    const staff_mobile = '**********';
    const password = '********';
    
    const staff = await Staff.findByMobile(staff_mobile);
    
    if (!staff) {
      console.log('❌ Staff not found');
      return;
    }
    
    console.log('✅ Staff found:', staff.staff_name);
    
    if (!staff.is_active) {
      console.log('❌ Staff account is not active');
      return;
    }
    
    if (!staff.password) {
      console.log('❌ Staff has no password set');
      return;
    }
    
    const isPasswordValid = await bcrypt.compare(password, staff.password);
    
    if (!isPasswordValid) {
      console.log('❌ Invalid password');
      return;
    }
    
    console.log('✅ Password valid');
    
    const accessToken = jwt.sign(
      {
        staffId: staff.id,
        mobile: staff.staff_mobile,
        type: 'mobile_staff'
      },
      process.env.JWT_SECRET || 'your_jwt_secret',
      { expiresIn: '24h' }
    );
    
    const refreshToken = jwt.sign(
      {
        staffId: staff.id,
        type: 'mobile_staff_refresh'
      },
      process.env.JWT_REFRESH_SECRET || 'your_jwt_refresh_secret',
      { expiresIn: '7d' }
    );
    
    console.log('✅ Tokens generated successfully');
    console.log('Access Token:', accessToken.substring(0, 50) + '...');
    console.log('Refresh Token:', refreshToken.substring(0, 50) + '...');
    
    const staffData = {
      id: staff.id,
      name: staff.staff_name,
      mobile: staff.staff_mobile,
      email: staff.staff_email,
      department: {
        id: staff.department_id,
        name: staff.department_name
      },
      designation: {
        id: staff.designation_id,
        name: staff.designation_name
      }
    };
    
    console.log('✅ Staff data prepared:', JSON.stringify(staffData, null, 2));
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack:', error.stack);
  }
}

simpleLoginTest();
