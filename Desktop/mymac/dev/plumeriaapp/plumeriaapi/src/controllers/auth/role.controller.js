const RoleService = require('../../services/role.service');

// Get all roles
const getAllRoles = async (req, res) => {
  try {
    const includeInactive = req.query.includeInactive === 'true';
    const roles = await RoleService.getAllRoles(includeInactive);
    return res.json({
      success: true,
      data: roles
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching roles',
      error: error.message
    });
  }
};

// Get role by ID
const getRoleById = async (req, res) => {
  try {
    const { id } = req.params;
    const includeInactive = req.query.includeInactive === 'true';
    const role = await RoleService.getRoleById(id, includeInactive);
    
    return res.json({
      success: true,
      data: role
    });
  } catch (error) {
    if (error.message === 'Role not found' || error.message === 'Role is inactive') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Error fetching role',
      error: error.message
    });
  }
};

// Create a new role
const createRole = async (req, res) => {
  try {
    const { name, description, is_active } = req.body;
    
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Role name is required'
      });
    }
    
    // Get the current user's ID from the JWT token
    const createdBy = req.user.id;
    
    const role = await RoleService.createRole({ 
      name, 
      description, 
      is_active 
    }, createdBy);
    
    return res.status(201).json({
      success: true,
      message: 'Role created successfully',
      data: role
    });
  } catch (error) {
    if (error.message === 'Role name already exists') {
      return res.status(409).json({
        success: false,
        message: error.message
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Error creating role',
      error: error.message
    });
  }
};

// Update an existing role
const updateRole = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, is_active } = req.body;
    
    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Role name is required'
      });
    }
    
    const role = await RoleService.updateRole(id, { name, description, is_active });
    
    return res.json({
      success: true,
      message: 'Role updated successfully',
      data: role
    });
  } catch (error) {
    if (error.message === 'Role not found') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }
    
    if (error.message === 'Role name already exists') {
      return res.status(409).json({
        success: false,
        message: error.message
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Error updating role',
      error: error.message
    });
  }
};

// Delete a role
const deleteRole = async (req, res) => {
  try {
    const { id } = req.params;
    await RoleService.deleteRole(id);
    
    return res.json({
      success: true,
      message: 'Role deleted successfully'
    });
  } catch (error) {
    if (error.message === 'Role not found') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Error deleting role',
      error: error.message
    });
  }
};

// Toggle role active status
const toggleRoleStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;
    
    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: 'is_active parameter is required'
      });
    }
    
    const role = await RoleService.toggleRoleStatus(id, Boolean(is_active));
    
    return res.json({
      success: true,
      message: `Role ${is_active ? 'activated' : 'deactivated'} successfully`,
      data: role
    });
  } catch (error) {
    if (error.message === 'Role not found') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }
    
    return res.status(500).json({
      success: false,
      message: `Error ${req.body.is_active ? 'activating' : 'deactivating'} role`,
      error: error.message
    });
  }
};

// Assign permission to role
const assignPermission = async (req, res) => {
  try {
    const { roleId, permissionId } = req.params;
    
    await RoleService.assignPermission(roleId, permissionId);
    
    return res.json({
      success: true,
      message: 'Permission assigned to role successfully'
    });
  } catch (error) {
    if (error.message === 'Role not found' || error.message === 'Permission not found') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Error assigning permission',
      error: error.message
    });
  }
};

// Remove permission from role
const removePermission = async (req, res) => {
  try {
    const { roleId, permissionId } = req.params;
    
    await RoleService.removePermission(roleId, permissionId);
    
    return res.json({
      success: true,
      message: 'Permission removed from role successfully'
    });
  } catch (error) {
    if (error.message === 'Role not found' || error.message === 'Permission not found') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Error removing permission',
      error: error.message
    });
  }
};

// Assign role to user
const assignRoleToUser = async (req, res) => {
  try {
    const { userId, roleId } = req.params;
    
    await RoleService.assignRoleToUser(userId, roleId);
    
    return res.json({
      success: true,
      message: 'Role assigned to user successfully'
    });
  } catch (error) {
    if (error.message === 'Role not found') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }
    
    if (error.message === 'Cannot assign inactive role') {
      return res.status(400).json({
        success: false,
        message: error.message
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Error assigning role to user',
      error: error.message
    });
  }
};

// Remove role from user
const removeRoleFromUser = async (req, res) => {
  try {
    const { userId, roleId } = req.params;
    
    await RoleService.removeRoleFromUser(userId, roleId);
    
    return res.json({
      success: true,
      message: 'Role removed from user successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error removing role from user',
      error: error.message
    });
  }
};

// Get users with role
const getUsersWithRole = async (req, res) => {
  try {
    const { roleId } = req.params;
    
    const users = await RoleService.getUsersWithRole(roleId);
    
    return res.json({
      success: true,
      data: users
    });
  } catch (error) {
    if (error.message === 'Role not found') {
      return res.status(404).json({
        success: false,
        message: error.message
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Error fetching users with role',
      error: error.message
    });
  }
};

module.exports = {
  getAllRoles,
  getRoleById,
  createRole,
  updateRole,
  deleteRole,
  toggleRoleStatus,
  assignPermission,
  removePermission,
  assignRoleToUser,
  removeRoleFromUser,
  getUsersWithRole
};