// src/controllers/auth/user.controller.js

const User = require('../../models/auth/user.model');
const Role = require('../../models/auth/role.model');
const bcrypt = require('bcrypt');
const db = require('../../config/database');

// Get all users
const getAllUsers = async (req, res) => {
  try {
    // Use the findAll method from the User model
    const users = await User.findAll();

    // Get roles for each user
    const usersWithRoles = await Promise.all(users.map(async (user) => {
      const roles = await User.getUserRoles(user.id);
      return {
        ...user,
        roles: roles.map(role => role.name)
      };
    }));

    return res.json({
      success: true,
      data: usersWithRoles
    });
  } catch (error) {
    console.error('Error in getAllUsers:', error);
    return res.status(500).json({
      success: false,
      message: 'Error fetching users',
      error: error.message
    });
  }
};

// Get user by ID
const getUserById = async (req, res) => {
  try {
    const { id } = req.params;
    const user = await User.findById(id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get roles for the user
    const roles = await User.getUserRoles(user.id);

    return res.json({
      success: true,
      data: {
        ...user,
        roles: roles.map(role => role.name)
      }
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching user',
      error: error.message
    });
  }
};

// Create a new user
const createUser = async (req, res) => {
  try {
    const { username, email, password, name, phone, is_active, roles } = req.body;

    // Validate required fields
    if (!username || !email || !password || !name) {
      return res.status(400).json({
        success: false,
        message: 'Required fields are missing'
      });
    }

    // Check if username or email already exists
    const existingEmail = await User.findByEmail(email);
    if (existingEmail) {
      return res.status(409).json({
        success: false,
        message: 'Email already in use'
      });
    }

    const existingUsername = await User.findByUsername(username);
    if (existingUsername) {
      return res.status(409).json({
        success: false,
        message: 'Username already in use'
      });
    }

    // Create the user
    const userData = {
      username,
      email,
      password,
      name,
      phone,
      is_active: is_active !== undefined ? is_active : true
    };

    const user = await User.create(userData);

    // Assign roles if provided
    if (roles && roles.length > 0) {
      const assignRolePromises = roles.map(async (roleName) => {
        const role = await Role.findByName(roleName);
        if (role) {
          await Role.assignToUser(user.id, role.id);
        }
      });

      await Promise.all(assignRolePromises);
    }

    // Get the assigned roles
    const assignedRoles = await User.getUserRoles(user.id);

    return res.status(201).json({
      success: true,
      message: 'User created successfully',
      data: {
        ...user,
        roles: assignedRoles.map(role => role.name)
      }
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error creating user',
      error: error.message
    });
  }
};

// Update an existing user
const updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const { username, email, password, name, phone, is_active, roles } = req.body;

    console.log('Update request received for user:', id);
    console.log('Roles from request:', roles);

    // Check if user exists
    const user = await User.findById(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if username or email already exists (if changing)
    if (email && email !== user.email) {
      const existingEmail = await User.findByEmail(email);
      if (existingEmail) {
        return res.status(409).json({
          success: false,
          message: 'Email already in use'
        });
      }
    }

    if (username && username !== user.username) {
      const existingUsername = await User.findByUsername(username);
      if (existingUsername) {
        return res.status(409).json({
          success: false,
          message: 'Username already in use'
        });
      }
    }

    // Prepare update data
    const updateData = { username, email, name, phone, is_active };

    // Handle password update if provided
    if (password) {
      updateData.password = password;
    }

    // Update user using the User model method
    await User.update(id, updateData);

    // Update roles if provided - completely replace existing roles
    if (roles && Array.isArray(roles)) {
      try {
        // First, remove all existing roles for this user
        console.log('Removing existing roles for user:', id);
        const deleteQuery = 'DELETE FROM user_roles WHERE user_id = ?';
        await db.query(deleteQuery, [id]);

        // Then assign new roles
        if (roles.length > 0) {
          console.log('Assigning new roles:', roles);
          const rolePromises = roles.map(async (roleName) => {
            try {
              const role = await Role.findByName(roleName);
              if (role) {
                console.log(`Found role ${roleName} with ID ${role.id}`);
                return Role.assignToUser(id, role.id);
              } else {
                console.log(`Role ${roleName} not found`);
                return null;
              }
            } catch (roleError) {
              console.error(`Error finding role ${roleName}:`, roleError);
              return null;
            }
          });

          await Promise.all(rolePromises.filter(p => p !== null));
        }
      } catch (roleError) {
        console.error('Error updating roles:', roleError);
        // Continue with the update even if role assignment fails
      }
    }

    // Get updated user data
    const updatedUser = await User.findById(id);
    const updatedRoles = await User.getUserRoles(id);

    console.log('Updated user data:', updatedUser);
    console.log('Updated roles:', updatedRoles.map(r => r.name));

    return res.json({
      success: true,
      message: 'User updated successfully',
      data: {
        ...updatedUser,
        roles: updatedRoles.map(role => role.name)
      }
    });
  } catch (error) {
    console.error('Error updating user:', error);
    return res.status(500).json({
      success: false,
      message: 'Error updating user',
      error: error.message
    });
  }
};

// Delete a user
const deleteUser = async (req, res) => {
    try {
      const { id } = req.params;

      // Check if user exists
      const user = await User.findById(id);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Delete user using model method
      await User.delete(id);

      return res.json({
        success: true,
        message: 'User deleted successfully'
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: 'Error deleting user',
        error: error.message
      });
    }
  };

// Toggle user status
const toggleUserStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;

    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: 'is_active parameter is required'
      });
    }

    // Check if user exists
    const user = await User.findById(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update active status
    await User.update(id, { is_active });

    // Get updated user data
    const updatedUser = await User.findById(id);
    const roles = await User.getUserRoles(id);

    return res.json({
      success: true,
      message: `User ${is_active ? 'activated' : 'deactivated'} successfully`,
      data: {
        ...updatedUser,
        roles: roles.map(role => role.name)
      }
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: `Error ${req.body.is_active ? 'activating' : 'deactivating'} user`,
      error: error.message
    });
  }
};

// Change user password
const changePassword = async (req, res) => {
  try {
    const { id } = req.params;
    const { currentPassword, newPassword } = req.body;
    const authenticatedUserId = req.user.id; // Get the ID of the authenticated user

    // Validate required fields
    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: 'Current password and new password are required'
      });
    }

    // Check if user exists
    const user = await User.findById(id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if the authenticated user is changing their own password or has admin role
    const isOwnPassword = authenticatedUserId === parseInt(id);
    let isAdmin = false;

    if (!isOwnPassword) {
      // Check if the authenticated user has admin role
      const userRoles = await User.getUserRoles(authenticatedUserId);
      isAdmin = userRoles.some(role => role.name === 'admin');

      if (!isAdmin) {
        return res.status(403).json({
          success: false,
          message: 'You can only change your own password'
        });
      }
    }

    // Verify current password (only if changing own password)
    if (isOwnPassword) {
      const isPasswordValid = await User.verifyPassword(currentPassword, user.password);
      if (!isPasswordValid) {
        return res.status(401).json({
          success: false,
          message: 'Current password is incorrect'
        });
      }
    }

    // Update the password - the User.update method will hash it
    await User.update(id, { password: newPassword });

    return res.json({
      success: true,
      message: 'Password changed successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error changing password',
      error: error.message
    });
  }
};

module.exports = {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  toggleUserStatus,
  changePassword
};