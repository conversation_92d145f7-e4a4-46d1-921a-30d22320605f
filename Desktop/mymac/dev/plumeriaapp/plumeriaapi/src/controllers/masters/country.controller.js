const Country = require('../../models/masters/country.model');

// Get all countries
const getAllCountries = async (req, res) => {
  try {
    const includeInactive = req.query.includeInactive === 'true';
    const countries = await Country.findAll(includeInactive);
    
    return res.json({
      success: true,
      data: countries
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching countries',
      error: error.message
    });
  }
};

// Get country by ID
const getCountryById = async (req, res) => {
  try {
    const { id } = req.params;
    const country = await Country.findById(id);
    
    if (!country) {
      return res.status(404).json({
        success: false,
        message: 'Country not found'
      });
    }
    
    return res.json({
      success: true,
      data: country
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error fetching country',
      error: error.message
    });
  }
};

// Create a new country
const createCountry = async (req, res) => {
  try {
    const { name, code, is_active } = req.body;
    
    if (!name || !code) {
      return res.status(400).json({
        success: false,
        message: 'Country name and code are required'
      });
    }
    
    // Check if country name already exists
    const existingCountryWithName = await Country.findByName(name);
    if (existingCountryWithName) {
      return res.status(409).json({
        success: false,
        message: 'Country name already exists'
      });
    }
    
    // Check if country code already exists
    const existingCountryWithCode = await Country.findByCode(code);
    if (existingCountryWithCode) {
      return res.status(409).json({
        success: false,
        message: 'Country code already exists'
      });
    }
    
    // Create the country with the authenticated user's ID
    const country = await Country.create({
      name,
      code,
      is_active,
      created_by: req.user.id
    });
    
    return res.status(201).json({
      success: true,
      message: 'Country created successfully',
      data: country
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error creating country',
      error: error.message
    });
  }
};

// Update an existing country
const updateCountry = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, is_active } = req.body;
    
    // Check if country exists
    const country = await Country.findById(id);
    if (!country) {
      return res.status(404).json({
        success: false,
        message: 'Country not found'
      });
    }
    
    // Check if name is being changed and if it already exists
    if (name && name !== country.name) {
      const existingCountry = await Country.findByName(name);
      if (existingCountry) {
        return res.status(409).json({
          success: false,
          message: 'Country name already exists'
        });
      }
    }
    
    // Check if code is being changed and if it already exists
    if (code && code !== country.code) {
      const existingCountry = await Country.findByCode(code);
      if (existingCountry) {
        return res.status(409).json({
          success: false,
          message: 'Country code already exists'
        });
      }
    }
    
    // Update the country with the authenticated user's ID as the updater
    const updatedCountry = await Country.update(id, {
      name,
      code,
      is_active,
      updated_by: req.user.id
    });
    
    return res.json({
      success: true,
      message: 'Country updated successfully',
      data: updatedCountry
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error updating country',
      error: error.message
    });
  }
};

// Delete a country
const deleteCountry = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Check if country exists
    const country = await Country.findById(id);
    if (!country) {
      return res.status(404).json({
        success: false,
        message: 'Country not found'
      });
    }
    
    // Delete the country
    await Country.delete(id);
    
    return res.json({
      success: true,
      message: 'Country deleted successfully'
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error deleting country',
      error: error.message
    });
  }
};

// Toggle country active status
const toggleCountryStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { is_active } = req.body;
    
    if (is_active === undefined) {
      return res.status(400).json({
        success: false,
        message: 'is_active parameter is required'
      });
    }
    
    // Check if country exists
    const country = await Country.findById(id);
    if (!country) {
      return res.status(404).json({
        success: false,
        message: 'Country not found'
      });
    }
    
    // Toggle the status
    const updatedCountry = await Country.toggleActive(id, Boolean(is_active), req.user.id);
    
    return res.json({
      success: true,
      message: `Country ${is_active ? 'activated' : 'deactivated'} successfully`,
      data: updatedCountry
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: `Error ${req.body.is_active ? 'activating' : 'deactivating'} country`,
      error: error.message
    });
  }
};

// Bulk delete countries
const bulkDeleteCountries = async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Country IDs array is required'
      });
    }
    
    // Check if all countries exist
    const validationPromises = ids.map(id => Country.findById(id));
    const countries = await Promise.all(validationPromises);
    
    const notFoundIds = ids.filter((id, index) => !countries[index]);
    
    if (notFoundIds.length > 0) {
      return res.status(404).json({
        success: false,
        message: `Countries not found with IDs: ${notFoundIds.join(', ')}`
      });
    }
    
    // Perform bulk delete
    await Country.bulkDelete(ids);
    
    return res.json({
      success: true,
      message: `Successfully deleted ${ids.length} countries`
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Error performing bulk delete operation',
      error: error.message
    });
  }
};

module.exports = {
  getAllCountries,
  getCountryById,
  createCountry,
  updateCountry,
  deleteCountry,
  toggleCountryStatus,
  bulkDeleteCountries
};