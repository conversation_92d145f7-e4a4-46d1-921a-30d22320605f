const express = require('express');
const router = express.Router();

/**
 * Mobile API Routes Index
 * Base URL: /api/mobile/v1/
 */

// Import route modules
const authRoutes = require('./auth.routes');

// Mobile API Info endpoint
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Plumeria Mobile API v1.0',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: {
      authentication: '/auth',
      documentation: '/docs',
      health: '/health'
    },
    supported_platforms: ['android', 'ios'],
    minimum_app_version: '1.0.0'
  });
});

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Mobile API is healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory_usage: process.memoryUsage(),
    version: '1.0.0'
  });
});

// Mount route modules
router.use('/auth', authRoutes);

// 404 handler for mobile API routes
router.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'Mobile API endpoint not found',
    timestamp: new Date().toISOString(),
    status_code: 404,
    meta: {
      api_version: 'v1',
      requested_path: req.originalUrl
    }
  });
});

module.exports = router;