/**
 * Mobile API Response Utility
 * Provides consistent response format for mobile applications
 */

const mobileResponse = {
  /**
   * Success response
   * @param {Object} res - Express response object
   * @param {Object} data - Response data
   * @param {String} message - Success message
   * @param {Number} statusCode - HTTP status code (default: 200)
   */
  success: (res, data = {}, message = 'Success', statusCode = 200) => {
    const response = {
      success: true,
      message: message,
      data: data,
      timestamp: new Date().toISOString(),
      status_code: statusCode
    };

    // Add metadata for mobile apps
    response.meta = {
      api_version: 'v1',
      response_time: Date.now()
    };

    return res.status(statusCode).json(response);
  },

  /**
   * Error response
   * @param {Object} res - Express response object
   * @param {String} message - Error message
   * @param {Number} statusCode - HTTP status code (default: 400)
   * @param {Object} errors - Detailed error information
   */
  error: (res, message = 'Error', statusCode = 400, errors = null) => {
    const response = {
      success: false,
      message: message,
      timestamp: new Date().toISOString(),
      status_code: statusCode
    };

    // Add error details if provided
    if (errors) {
      response.errors = errors;
    }

    // Add metadata for mobile apps
    response.meta = {
      api_version: 'v1',
      response_time: Date.now()
    };

    return res.status(statusCode).json(response);
  },

  /**
   * Validation error response
   * @param {Object} res - Express response object
   * @param {Array|Object} validationErrors - Validation error details
   */
  validationError: (res, validationErrors) => {
    return mobileResponse.error(res, 'Validation failed', 422, {
      validation: validationErrors
    });
  },

  /**
   * Authentication error response
   * @param {Object} res - Express response object
   * @param {String} message - Error message
   */
  authError: (res, message = 'Authentication failed') => {
    return mobileResponse.error(res, message, 401);
  },

  /**
   * Authorization error response
   * @param {Object} res - Express response object
   * @param {String} message - Error message
   */
  forbiddenError: (res, message = 'Access forbidden') => {
    return mobileResponse.error(res, message, 403);
  },

  /**
   * Not found error response
   * @param {Object} res - Express response object
   * @param {String} message - Error message
   */
  notFoundError: (res, message = 'Resource not found') => {
    return mobileResponse.error(res, message, 404);
  },

  /**
   * Server error response
   * @param {Object} res - Express response object
   * @param {String} message - Error message
   */
  serverError: (res, message = 'Internal server error') => {
    return mobileResponse.error(res, message, 500);
  },

  /**
   * Paginated response
   * @param {Object} res - Express response object
   * @param {Array} data - Array of data items
   * @param {Object} pagination - Pagination metadata
   * @param {String} message - Success message
   */
  paginated: (res, data, pagination, message = 'Data retrieved successfully') => {
    const response = {
      success: true,
      message: message,
      data: data,
      pagination: {
        current_page: pagination.currentPage || 1,
        total_pages: pagination.totalPages || 1,
        total_count: pagination.totalCount || data.length,
        per_page: pagination.perPage || data.length,
        has_next: pagination.hasNext || false,
        has_prev: pagination.hasPrev || false
      },
      timestamp: new Date().toISOString(),
      status_code: 200
    };

    // Add metadata for mobile apps
    response.meta = {
      api_version: 'v1',
      response_time: Date.now()
    };

    return res.status(200).json(response);
  }
};

/**
 * Validation helper functions
 */
const validation = {
  /**
   * Check if required fields are present
   * @param {Object} body - Request body
   * @param {Array} requiredFields - Array of required field names
   * @returns {Array} Array of missing fields
   */
  checkRequiredFields: (body, requiredFields) => {
    const missingFields = [];
    
    requiredFields.forEach(field => {
      if (!body[field] || (typeof body[field] === 'string' && body[field].trim() === '')) {
        missingFields.push(field);
      }
    });

    return missingFields;
  },

  /**
   * Validate mobile number format
   * @param {String} mobile - Mobile number
   * @returns {Boolean} Is valid mobile number
   */
  isValidMobile: (mobile) => {
    // Indian mobile number validation (10 digits starting with 6-9)
    const mobileRegex = /^[6-9]\d{9}$/;
    return mobileRegex.test(mobile);
  },

  /**
   * Validate email format
   * @param {String} email - Email address
   * @returns {Boolean} Is valid email
   */
  isValidEmail: (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * Validate password strength
   * @param {String} password - Password
   * @returns {Object} Validation result with strength score
   */
  validatePassword: (password) => {
    const result = {
      isValid: false,
      strength: 'weak',
      errors: []
    };

    if (!password) {
      result.errors.push('Password is required');
      return result;
    }

    if (password.length < 6) {
      result.errors.push('Password must be at least 6 characters long');
    }

    if (password.length < 8) {
      result.strength = 'weak';
    } else if (password.length >= 8 && /(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
      result.strength = 'strong';
    } else {
      result.strength = 'medium';
    }

    result.isValid = result.errors.length === 0;
    return result;
  }
};

module.exports = {
  mobileResponse,
  validation
};