const db = require('../../config/database');

const Permission = {
  // Get all permissions
  findAll: async () => {
    const query = 'SELECT * FROM permissions';
    return await db.query(query);
  },

  // Get permission by ID
  findById: async (id) => {
    const query = 'SELECT * FROM permissions WHERE id = ?';
    const permissions = await db.query(query, [id]);
    return permissions[0];
  },

  // Get permission by name
  findByName: async (name) => {
    const query = 'SELECT * FROM permissions WHERE name = ?';
    const permissions = await db.query(query, [name]);
    return permissions[0];
  },

  // Create new permission
  create: async (permissionData) => {
    const { name, description } = permissionData;
    const query = 'INSERT INTO permissions (name, description) VALUES (?, ?)';
    const result = await db.query(query, [name, description]);
    return { id: result.insertId, name, description };
  },

  // Update permission
  update: async (id, permissionData) => {
    const { name, description } = permissionData;
    const query = 'UPDATE permissions SET name = ?, description = ? WHERE id = ?';
    await db.query(query, [name, description, id]);
    return { id, name, description };
  },

  // Delete permission
  delete: async (id) => {
    const query = 'DELETE FROM permissions WHERE id = ?';
    return await db.query(query, [id]);
  },

  // Get modules with this permission
  getModulesWithPermission: async (permissionId) => {
    const query = `
      SELECT m.* FROM modules m
      JOIN role_module_permissions rmp ON m.id = rmp.module_id
      WHERE rmp.permission_id = ?
      GROUP BY m.id
    `;
    return await db.query(query, [permissionId]);
  },

  // Get roles with this permission
  getRolesWithPermission: async (permissionId) => {
    const query = `
      SELECT r.* FROM roles r
      JOIN role_module_permissions rmp ON r.id = rmp.role_id
      WHERE rmp.permission_id = ?
      GROUP BY r.id
    `;
    return await db.query(query, [permissionId]);
  }
};

module.exports = Permission;