const bcrypt = require('bcrypt');
const db = require('../../config/database');

// User model with all necessary methods
const User = {
  // Find a user by email
  findByEmail: async (email) => {
    const query = 'SELECT * FROM users WHERE email = ?';
    const users = await db.query(query, [email]);
    return users[0];
  },

  // Find a user by username
  findByUsername: async (username) => {
    const query = 'SELECT * FROM users WHERE username = ?';
    const users = await db.query(query, [username]);
    return users[0];
  },

  delete: async (id) => {
    const query = 'DELETE FROM users WHERE id = ?';
    return await db.query(query, [id]);
  },

  // Find a user by ID
  findById: async (id) => {
    const query = 'SELECT * FROM users WHERE id = ?';
    const users = await db.query(query, [id]);
    return users[0];
  },

  // Find a user by either email OR username
  findByEmailOrUsername: async (identifier) => {
    const query = 'SELECT * FROM users WHERE email = ? OR username = ?';
    const users = await db.query(query, [identifier, identifier]);
    return users[0];
  },

  // Create a new user
  create: async (userData) => {
    const { username, email, password, name, phone } = userData;
    
    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 10);
    
    const query = `
      INSERT INTO users (username, email, password, name, phone)
      VALUES (?, ?, ?, ?, ?)
    `;
    
    const result = await db.query(query, [
      username,
      email,
      hashedPassword,
      name,
      phone || null
    ]);
    
    return { id: result.insertId, username, email, name, phone };
  },

  // Verify a password against a hash
  verifyPassword: async (plainPassword, hashedPassword) => {
    return await bcrypt.compare(plainPassword, hashedPassword);
  },

  // Get roles for a user
  getUserRoles: async (userId) => {
    const query = `
      SELECT r.* FROM roles r
      JOIN user_roles ur ON r.id = ur.role_id
      WHERE ur.user_id = ?
    `;
    
    return await db.query(query, [userId]);
  }, 

  // Update a user - this is the main method that will be used for updates including password
  update: async (id, userData) => {
    // Start building the query
    let query = 'UPDATE users SET ';
    const params = [];
    
    // Add fields to update
    if (userData.username) {
      query += 'username = ?, ';
      params.push(userData.username);
    }
    
    if (userData.email) {
      query += 'email = ?, ';
      params.push(userData.email);
    }
    
    if (userData.name) {
      query += 'name = ?, ';
      params.push(userData.name);
    }
    
    if (userData.phone !== undefined) {
      query += 'phone = ?, ';
      params.push(userData.phone);
    }
    
    if (userData.is_active !== undefined) {
      query += 'is_active = ?, ';
      params.push(userData.is_active ? 1 : 0);
    }
    
    // Add password update if provided
    if (userData.password) {
      console.log('Updating password in database');
      // Hash the password
      const hashedPassword = await bcrypt.hash(userData.password, 10);
      query += 'password = ?, ';
      params.push(hashedPassword);
    }
    
    // Remove trailing comma and space
    query = query.slice(0, -2);
    
    // Add WHERE clause
    query += ' WHERE id = ?';
    params.push(id);
    
    // Execute the query
    console.log('Executing SQL query with password update:', userData.password ? 'Yes' : 'No');
    return await db.query(query, params);
  },
  
  // Get all users
  findAll: async (includeInactive = true) => {
    let query = 'SELECT * FROM users';
    if (!includeInactive) {
      query += ' WHERE is_active = 1';
    }
    return await db.query(query);
  },

};

module.exports = User;