const db = require('../../config/database');

const Country = {
  // Get all countries
  findAll: async (includeInactive = false) => {
    let query = `
      SELECT c.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM countries c
      LEFT JOIN users u1 ON c.created_by = u1.id
      LEFT JOIN users u2 ON c.updated_by = u2.id
    `;
    
    if (!includeInactive) {
      query += ' WHERE c.is_active = 1';
    }
    
    return await db.query(query);
  },

  // Get country by ID
  findById: async (id) => {
    const query = `
      SELECT c.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM countries c
      LEFT JOIN users u1 ON c.created_by = u1.id
      LEFT JOIN users u2 ON c.updated_by = u2.id
      WHERE c.id = ?
    `;
    
    const countries = await db.query(query, [id]);
    return countries[0];
  },

  // Get country by name
  findByName: async (name) => {
    const query = 'SELECT * FROM countries WHERE name = ?';
    const countries = await db.query(query, [name]);
    return countries[0];
  },

  // Get country by code
  findByCode: async (code) => {
    const query = 'SELECT * FROM countries WHERE code = ?';
    const countries = await db.query(query, [code]);
    return countries[0];
  },

  // Create new country
  create: async (countryData) => {
    const { name, code, is_active = 1, created_by } = countryData;
    
    const query = `
      INSERT INTO countries (name, code, is_active, created_by) 
      VALUES (?, ?, ?, ?)
    `;
    
    const result = await db.query(query, [name, code, is_active, created_by]);
    return { id: result.insertId, name, code, is_active, created_by };
  },

  // Update country
  update: async (id, countryData) => {
    const { name, code, is_active, updated_by } = countryData;
    
    let query = 'UPDATE countries SET ';
    const params = [];
    
    if (name !== undefined) {
      query += 'name = ?, ';
      params.push(name);
    }
    
    if (code !== undefined) {
      query += 'code = ?, ';
      params.push(code);
    }
    
    if (is_active !== undefined) {
      query += 'is_active = ?, ';
      params.push(is_active);
    }
    
    query += 'updated_by = ? ';
    params.push(updated_by);
    
    query += 'WHERE id = ?';
    params.push(id);
    
    await db.query(query, params);
    
    return { id, ...countryData };
  },

  // Delete country
  delete: async (id) => {
    const query = 'DELETE FROM countries WHERE id = ?';
    return await db.query(query, [id]);
  },

  // Bulk delete countries
  bulkDelete: async (ids) => {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid country IDs for bulk deletion');
    }

    const placeholders = ids.map(() => '?').join(',');
    const query = `DELETE FROM countries WHERE id IN (${placeholders})`;

    return await db.query(query, ids);
  },

  // Toggle country active status
  toggleActive: async (id, isActive, updatedBy) => {
    const query = 'UPDATE countries SET is_active = ?, updated_by = ? WHERE id = ?';
    await db.query(query, [isActive ? 1 : 0, updatedBy, id]);
    return { id, is_active: isActive };
  }
};

module.exports = Country;