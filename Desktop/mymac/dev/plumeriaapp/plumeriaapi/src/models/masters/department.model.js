// src/models/masters/department.model.js
const db = require('../../config/database');

const Department = {
  // Get all departments
  findAll: async (includeInactive = false) => {
    let query = `
      SELECT d.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM departments d
      LEFT JOIN users u1 ON d.created_by = u1.id
      LEFT JOIN users u2 ON d.updated_by = u2.id
    `;
    
    if (!includeInactive) {
      query += ' WHERE d.is_active = 1';
    }
    
    return await db.query(query);
  },

  // Get department by ID
  findById: async (id) => {
    const query = `
      SELECT d.*, 
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM departments d
      LEFT JOIN users u1 ON d.created_by = u1.id
      LEFT JOIN users u2 ON d.updated_by = u2.id
      WHERE d.id = ?
    `;
    
    const departments = await db.query(query, [id]);
    return departments[0];
  },

  // Get department by name
  findByName: async (name) => {
    const query = 'SELECT * FROM departments WHERE name = ?';
    const departments = await db.query(query, [name]);
    return departments[0];
  },

  // Get department by code
  findByCode: async (code) => {
    const query = 'SELECT * FROM departments WHERE code = ?';
    const departments = await db.query(query, [code]);
    return departments[0];
  },

  // Create new department
  create: async (departmentData) => {
    const { name, code, is_active = 1, created_by } = departmentData;
    
    const query = `
      INSERT INTO departments (name, code, is_active, created_by) 
      VALUES (?, ?, ?, ?)
    `;
    
    const result = await db.query(query, [name, code, is_active, created_by]);
    return { id: result.insertId, name, code, is_active, created_by };
  },

  // Update department
  update: async (id, departmentData) => {
    const { name, code, is_active, updated_by } = departmentData;
    
    let query = 'UPDATE departments SET ';
    const params = [];
    
    if (name !== undefined) {
      query += 'name = ?, ';
      params.push(name);
    }
    
    if (code !== undefined) {
      query += 'code = ?, ';
      params.push(code);
    }
    
    if (is_active !== undefined) {
      query += 'is_active = ?, ';
      params.push(is_active);
    }
    
    query += 'updated_by = ? ';
    params.push(updated_by);
    
    query += 'WHERE id = ?';
    params.push(id);
    
    await db.query(query, params);
    
    return { id, ...departmentData };
  },

  // Delete department
  delete: async (id) => {
    const query = 'DELETE FROM departments WHERE id = ?';
    return await db.query(query, [id]);
  },

  // Bulk delete departments
  bulkDelete: async (ids) => {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid department IDs for bulk deletion');
    }

    const placeholders = ids.map(() => '?').join(',');
    const query = `DELETE FROM departments WHERE id IN (${placeholders})`;

    return await db.query(query, ids);
  },

  // Toggle department active status
  toggleActive: async (id, isActive, updatedBy) => {
    const query = 'UPDATE departments SET is_active = ?, updated_by = ? WHERE id = ?';
    await db.query(query, [isActive ? 1 : 0, updatedBy, id]);
    return { id, is_active: isActive };
  }
};

module.exports = Department;
