const db = require('../../config/database');

const State = {
  // Get all states
  findAll: async (includeInactive = false) => {
    let query = `
      SELECT s.*, 
             c.name as country_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM states s
      LEFT JOIN countries c ON s.country_id = c.id
      LEFT JOIN users u1 ON s.created_by = u1.id
      LEFT JOIN users u2 ON s.updated_by = u2.id
    `;
    
    if (!includeInactive) {
      query += ' WHERE s.is_active = 1';
    }
    
    return await db.query(query);
  },

  // Get all states by country
  findByCountry: async (countryId, includeInactive = false) => {
    let query = `
      SELECT s.*, 
             c.name as country_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM states s
      LEFT JOIN countries c ON s.country_id = c.id
      LEFT JOIN users u1 ON s.created_by = u1.id
      LEFT JOIN users u2 ON s.updated_by = u2.id
      WHERE s.country_id = ?
    `;
    
    if (!includeInactive) {
      query += ' AND s.is_active = 1';
    }
    
    return await db.query(query, [countryId]);
  },

  // Get state by ID
  findById: async (id) => {
    const query = `
      SELECT s.*, 
             c.name as country_name,
             u1.username as created_by_username,
             u2.username as updated_by_username
      FROM states s
      LEFT JOIN countries c ON s.country_id = c.id
      LEFT JOIN users u1 ON s.created_by = u1.id
      LEFT JOIN users u2 ON s.updated_by = u2.id
      WHERE s.id = ?
    `;
    
    const states = await db.query(query, [id]);
    return states[0];
  },

  // Get state by name and country
  findByName: async (name, countryId) => {
    const query = 'SELECT * FROM states WHERE name = ? AND country_id = ?';
    const states = await db.query(query, [name, countryId]);
    return states[0];
  },

  // Get state by code and country
  findByCode: async (code, countryId) => {
    const query = 'SELECT * FROM states WHERE code = ? AND country_id = ?';
    const states = await db.query(query, [code, countryId]);
    return states[0];
  },

  // Create new state
  create: async (stateData) => {
    const { name, code, country_id, is_active = 1, created_by } = stateData;
    
    const query = `
      INSERT INTO states (name, code, country_id, is_active, created_by) 
      VALUES (?, ?, ?, ?, ?)
    `;
    
    const result = await db.query(query, [name, code, country_id, is_active, created_by]);
    return { id: result.insertId, name, code, country_id, is_active, created_by };
  },

  // Update state
  update: async (id, stateData) => {
    const { name, code, country_id, is_active, updated_by } = stateData;
    
    let query = 'UPDATE states SET ';
    const params = [];
    
    if (name !== undefined) {
      query += 'name = ?, ';
      params.push(name);
    }
    
    if (code !== undefined) {
      query += 'code = ?, ';
      params.push(code);
    }
    
    if (country_id !== undefined) {
      query += 'country_id = ?, ';
      params.push(country_id);
    }
    
    if (is_active !== undefined) {
      query += 'is_active = ?, ';
      params.push(is_active);
    }
    
    query += 'updated_by = ? ';
    params.push(updated_by);
    
    query += 'WHERE id = ?';
    params.push(id);
    
    await db.query(query, params);
    
    return { id, ...stateData };
  },

  // Delete state
  delete: async (id) => {
    const query = 'DELETE FROM states WHERE id = ?';
    return await db.query(query, [id]);
  },

  // Bulk delete states
  bulkDelete: async (ids) => {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid state IDs for bulk deletion');
    }

    const placeholders = ids.map(() => '?').join(',');
    const query = `DELETE FROM states WHERE id IN (${placeholders})`;

    return await db.query(query, ids);
  },

  // Toggle state active status
  toggleActive: async (id, isActive, updatedBy) => {
    const query = 'UPDATE states SET is_active = ?, updated_by = ? WHERE id = ?';
    await db.query(query, [isActive ? 1 : 0, updatedBy, id]);
    return { id, is_active: isActive };
  }
};

module.exports = State;