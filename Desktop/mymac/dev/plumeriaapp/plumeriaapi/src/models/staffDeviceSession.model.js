// src/models/staffDeviceSession.model.js
const db = require('../config/database');

const StaffDeviceSession = {
  /**
   * Create a new device session
   */
  createSession: async (sessionData) => {
    const {
      staff_id,
      device_id,
      session_token,
      refresh_token_hash,
      expires_at,
      ip_address,
      user_agent
    } = sessionData;

    const query = `
      INSERT INTO staff_device_sessions (
        staff_id, device_id, session_token, refresh_token_hash,
        expires_at, ip_address, user_agent
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await db.query(query, [
      staff_id, device_id, session_token, refresh_token_hash,
      expires_at, ip_address, user_agent
    ]);

    return await StaffDeviceSession.findById(result.insertId);
  },

  /**
   * Find session by ID
   */
  findById: async (id) => {
    const query = `
      SELECT sds.*, sd.device_id as device_identifier, sd.platform, sd.device_model
      FROM staff_device_sessions sds
      LEFT JOIN staff_devices sd ON sds.device_id = sd.id
      WHERE sds.id = ?
    `;
    
    const sessions = await db.query(query, [id]);
    return sessions[0];
  },

  /**
   * Find session by session token
   */
  findBySessionToken: async (session_token) => {
    const query = `
      SELECT sds.*, sd.device_id as device_identifier, sd.platform, sd.device_model
      FROM staff_device_sessions sds
      LEFT JOIN staff_devices sd ON sds.device_id = sd.id
      WHERE sds.session_token = ? AND sds.is_active = 1
    `;
    
    const sessions = await db.query(query, [session_token]);
    return sessions[0];
  },

  /**
   * Find active sessions for a staff member
   */
  findActiveSessionsByStaff: async (staff_id) => {
    const query = `
      SELECT sds.*, sd.device_id as device_identifier, sd.platform, 
             sd.device_model, sd.device_name
      FROM staff_device_sessions sds
      LEFT JOIN staff_devices sd ON sds.device_id = sd.id
      WHERE sds.staff_id = ? AND sds.is_active = 1 
      AND sds.expires_at > NOW()
      ORDER BY sds.login_time DESC
    `;
    
    return await db.query(query, [staff_id]);
  },

  /**
   * Find active sessions for a specific device
   */
  findActiveSessionsByDevice: async (device_id) => {
    const query = `
      SELECT * FROM staff_device_sessions 
      WHERE device_id = ? AND is_active = 1 
      AND expires_at > NOW()
      ORDER BY login_time DESC
    `;
    
    return await db.query(query, [device_id]);
  },

  /**
   * Update session activity (extend expiry, update IP, etc.)
   */
  updateSessionActivity: async (session_id, updates = {}) => {
    const allowedFields = ['expires_at', 'ip_address', 'user_agent'];
    const updateFields = [];
    const values = [];

    Object.keys(updates).forEach(field => {
      if (allowedFields.includes(field) && updates[field] !== undefined) {
        updateFields.push(`${field} = ?`);
        values.push(updates[field]);
      }
    });

    if (updateFields.length === 0) {
      return false;
    }

    updateFields.push('updated_at = NOW()');
    values.push(session_id);

    const query = `
      UPDATE staff_device_sessions 
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `;

    return await db.query(query, values);
  },

  /**
   * Logout session (mark as inactive)
   */
  logoutSession: async (session_token, logout_reason = 'manual') => {
    const query = `
      UPDATE staff_device_sessions 
      SET is_active = 0, logout_time = NOW(), logout_reason = ?
      WHERE session_token = ?
    `;
    
    return await db.query(query, [logout_reason, session_token]);
  },

  /**
   * Logout all sessions for a staff member
   */
  logoutAllStaffSessions: async (staff_id, logout_reason = 'forced') => {
    const query = `
      UPDATE staff_device_sessions 
      SET is_active = 0, logout_time = NOW(), logout_reason = ?
      WHERE staff_id = ? AND is_active = 1
    `;
    
    return await db.query(query, [logout_reason, staff_id]);
  },

  /**
   * Logout all sessions for a specific device
   */
  logoutDeviceSessions: async (device_id, logout_reason = 'device_removed') => {
    const query = `
      UPDATE staff_device_sessions 
      SET is_active = 0, logout_time = NOW(), logout_reason = ?
      WHERE device_id = ? AND is_active = 1
    `;
    
    return await db.query(query, [logout_reason, device_id]);
  },

  /**
   * Clean up expired sessions
   */
  cleanupExpiredSessions: async () => {
    const query = `
      UPDATE staff_device_sessions 
      SET is_active = 0, logout_time = NOW(), logout_reason = 'expired'
      WHERE expires_at < NOW() AND is_active = 1
    `;
    
    return await db.query(query);
  },

  /**
   * Get session statistics for a staff member
   */
  getSessionStats: async (staff_id, days = 30) => {
    const query = `
      SELECT 
        COUNT(*) as total_sessions,
        COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_sessions,
        COUNT(CASE WHEN login_time >= DATE_SUB(NOW(), INTERVAL ? DAY) THEN 1 END) as recent_sessions,
        MAX(login_time) as last_login,
        COUNT(DISTINCT device_id) as unique_devices
      FROM staff_device_sessions 
      WHERE staff_id = ?
    `;
    
    const stats = await db.query(query, [days, staff_id]);
    return stats[0];
  },

  /**
   * Delete old session records (for cleanup)
   */
  deleteOldSessions: async (days = 90) => {
    const query = `
      DELETE FROM staff_device_sessions 
      WHERE is_active = 0 
      AND (logout_time < DATE_SUB(NOW(), INTERVAL ? DAY) 
           OR (logout_time IS NULL AND login_time < DATE_SUB(NOW(), INTERVAL ? DAY)))
    `;
    
    return await db.query(query, [days, days]);
  },

  /**
   * Check if session is valid and active
   */
  isSessionValid: async (session_token) => {
    const query = `
      SELECT COUNT(*) as count
      FROM staff_device_sessions 
      WHERE session_token = ? 
      AND is_active = 1 
      AND expires_at > NOW()
    `;
    
    const result = await db.query(query, [session_token]);
    return result[0].count > 0;
  }
};

module.exports = StaffDeviceSession;
