// src/routes/masters/state.routes.js
const express = require('express');
const stateController = require('../../controllers/masters/state.controller');
const { verifyToken, hasModulePermission } = require('../../middleware/auth.middleware');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Routes for managing states
router.get('/', hasModulePermission('masters', 'read'), stateController.getAllStates);
router.get('/:id', hasModulePermission('masters', 'read'), stateController.getStateById);
router.post('/', hasModulePermission('masters', 'create'), stateController.createState);
router.put('/:id', hasModulePermission('masters', 'update'), stateController.updateState);
router.delete('/:id', hasModulePermission('masters', 'delete'), stateController.deleteState);
router.patch('/:id/status', hasModulePermission('masters', 'update'), stateController.toggleStateStatus);
router.post('/bulk-delete', hasModulePermission('masters', 'delete'), stateController.bulkDeleteStates);

module.exports = router;