const express = require('express');
const moduleController = require('../controllers/auth/module.controller');
const { verifyToken, hasModulePermission } = require('../middleware/auth.middleware');

const router = express.Router();

// Apply authentication middleware to all module routes
router.use(verifyToken);

// Routes for managing modules
router.get('/', hasModulePermission('module', 'read'), moduleController.getAllModules);
router.get('/:id', hasModulePermission('module', 'read'), moduleController.getModuleById);
router.post('/', hasModulePermission('module', 'create'), moduleController.createModule);
router.put('/:id', hasModulePermission('module', 'update'), moduleController.updateModule);
router.delete('/:id', hasModulePermission('module', 'delete'), moduleController.deleteModule);
router.patch('/:id/status', hasModulePermission('module', 'update'), moduleController.toggleModuleStatus);
router.post('/bulk-delete', hasModulePermission('module', 'delete'), moduleController.bulkDeleteModules);

// Routes for managing module permissions
router.post('/:moduleId/permissions/:permissionId', hasModulePermission('module', 'update'), moduleController.addPermissionToModule);
router.delete('/:moduleId/permissions/:permissionId', hasModulePermission('module', 'update'), moduleController.removePermissionFromModule);

// Routes for managing user module access
router.post('/users/:userId/:moduleId', hasModulePermission('module', 'update'), moduleController.grantUserAccess);
router.delete('/users/:userId/:moduleId', hasModulePermission('module', 'update'), moduleController.revokeUserAccess);
router.get('/:moduleId/users', hasModulePermission('module', 'read'), moduleController.getUsersWithAccess);

module.exports = router;