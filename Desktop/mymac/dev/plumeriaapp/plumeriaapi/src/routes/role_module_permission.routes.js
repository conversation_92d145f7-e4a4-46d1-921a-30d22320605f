const express = require('express');
const { verifyToken, hasModulePermission } = require('../middleware/auth.middleware');
const roleModulePermissionController = require('../controllers/auth/role_module_permission.controller');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(verifyToken);

// Get all role-module-permissions
router.get('/', 
  hasModulePermission('role', 'read'), 
  roleModulePermissionController.getAllRoleModulePermissions
);

// Get all permissions for a role
router.get('/roles/:roleId', 
  hasModulePermission('role', 'read'), 
  roleModulePermissionController.getRolePermissions
);

// Get permissions for a specific role-module combination
router.get('/roles/:roleId/modules/:moduleId', 
  hasModulePermission('role', 'read'), 
  roleModulePermissionController.getRoleModulePermissions
);

// Assign permission to role for a module
router.post('/roles/:roleId/modules/:moduleId/permissions/:permissionId', 
  hasModulePermission('role', 'update'), 
  roleModulePermissionController.assignRoleModulePermission
);

// Remove permission from role for a module
router.delete('/roles/:roleId/modules/:moduleId/permissions/:permissionId', 
  hasModulePermission('role', 'update'), 
  roleModulePermissionController.removeRoleModulePermission
);

// Assign all permissions of a module to a role
router.post('/roles/:roleId/modules/:moduleId/permissions', 
  hasModulePermission('role', 'update'), 
  roleModulePermissionController.assignAllModulePermissionsToRole
);

// Remove all permissions of a module from a role
router.delete('/roles/:roleId/modules/:moduleId/permissions', 
  hasModulePermission('role', 'update'), 
  roleModulePermissionController.removeAllModulePermissionsFromRole
);

module.exports = router;