const Module = require('../models/auth/module.model');
const Permission = require('../models/auth/permission.model');

class ModuleService {
  // Get all modules
  static async getAllModules(includeInactive = false) {
    return await Module.findAll(includeInactive);
  }

  // Get module by ID with its permissions
  static async getModuleById(id, includeInactive = false) {
    const module = await Module.findById(id);
    if (!module) {
      throw new Error('Module not found');
    }

    // If we don't want inactive modules and the module is inactive, throw error
    if (!includeInactive && !module.is_active) {
      throw new Error('Module is inactive');
    }

    const permissions = await Module.getModulePermissions(id);
    return {
      ...module,
      permissions
    };
  }

  // Create new module
  static async createModule(moduleData) {
    const existingModule = await Module.findByName(moduleData.name);
    if (existingModule) {
      throw new Error('Module name already exists');
    }

    const module = await Module.create(moduleData);

    // Generate standard CRUD permissions for this module
    // await this.generateStandardPermissions(module.id, module.name);

    return module;
  }

  // Generate standard CRUD permissions for a module
  static async generateStandardPermissions(moduleId, moduleName) {
    const standardActions = ['create', 'read', 'update', 'delete'];
    const createdPermissions = [];

    for (const action of standardActions) {
      const permissionName = `${moduleName}_${action}`;
      const existingPermission = await Permission.findByName(permissionName);

      // Skip if permission already exists
      if (existingPermission) {
        // Link existing permission to module
        await Module.addPermission(moduleId, existingPermission.id);
        createdPermissions.push(existingPermission);
        continue;
      }

      // Create new permission
      const permission = await Permission.create({
        name: permissionName,
        description: `${action} permission for ${moduleName} module`
      });

      // Link permission to module
      await Module.addPermission(moduleId, permission.id);
      createdPermissions.push(permission);
    }

    return createdPermissions;
  }

  // Update module
  static async updateModule(id, moduleData) {
    const module = await Module.findById(id);
    if (!module) {
      throw new Error('Module not found');
    }

    if (moduleData.name && moduleData.name !== module.name) {
      const existingModule = await Module.findByName(moduleData.name);
      if (existingModule) {
        throw new Error('Module name already exists');
      }
    }

    return await Module.update(id, moduleData);
  }

  // Delete module
  static async deleteModule(id) {
    const module = await Module.findById(id);
    if (!module) {
      throw new Error('Module not found');
    }

    await Module.delete(id);
    return { success: true };
  }

  // Toggle module active status
  static async toggleModuleStatus(id, isActive) {
    const module = await Module.findById(id);
    if (!module) {
      throw new Error('Module not found');
    }

    return await Module.toggleActive(id, isActive);
  }

  // Add custom permission to module
  static async addPermissionToModule(moduleId, permissionId) {
    const module = await Module.findById(moduleId);
    if (!module) {
      throw new Error('Module not found');
    }

    const permission = await Permission.findById(permissionId);
    if (!permission) {
      throw new Error('Permission not found');
    }

    await Module.addPermission(moduleId, permissionId);
    return { success: true };
  }

  // Remove permission from module
  static async removePermissionFromModule(moduleId, permissionId) {
    const module = await Module.findById(moduleId);
    if (!module) {
      throw new Error('Module not found');
    }

    const permission = await Permission.findById(permissionId);
    if (!permission) {
      throw new Error('Permission not found');
    }

    await Module.removePermission(moduleId, permissionId);
    return { success: true };
  }

  // Grant user access to module
  static async grantUserAccess(userId, moduleId) {
    const module = await Module.findById(moduleId);
    if (!module) {
      throw new Error('Module not found');
    }

    await Module.grantUserAccess(userId, moduleId);
    return { success: true };
  }

  // Revoke user access to module
  static async revokeUserAccess(userId, moduleId) {
    const module = await Module.findById(moduleId);
    if (!module) {
      throw new Error('Module not found');
    }

    await Module.revokeUserAccess(userId, moduleId);
    return { success: true };
  }

  // Get users with access to module
  static async getUsersWithAccess(moduleId) {
    const module = await Module.findById(moduleId);
    if (!module) {
      throw new Error('Module not found');
    }

    return await Module.getUsersWithAccess(moduleId);
  }

  // Bulk delete modules
  static async bulkDeleteModules(ids) {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('Invalid module IDs for bulk deletion');
    }

    // Validate that all modules exist
    const existingModules = [];
    const notFoundIds = [];

    for (const id of ids) {
      const module = await Module.findById(id);
      if (module) {
        existingModules.push(module);
      } else {
        notFoundIds.push(id);
      }
    }

    if (notFoundIds.length > 0) {
      throw new Error(`Modules not found with IDs: ${notFoundIds.join(', ')}`);
    }

    // Perform the bulk delete operation
    await Module.bulkDelete(ids);

    return {
      success: true,
      count: ids.length,
      deletedModules: existingModules.map(module => ({ id: module.id, name: module.name }))
    };
  }
}

module.exports = ModuleService;